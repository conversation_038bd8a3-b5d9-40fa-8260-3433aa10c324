#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
from typing import Dict, Any, List, Tuple

class ExtractHotWordsAndContent:
    """提取热词和内容的节点"""

    def __init__(self):
        """初始化提取器"""
        self.data_dir = os.path.join('data')

    def clean_content(self, text: str) -> str:
        """
        清理内容，移除自定义块和媒体块

        Args:
            text: 需要清理的文本

        Returns:
            str: 清理后的文本
        """
        # 移除 wbCustomBlock
        text = re.sub(r'```wbCustomBlock\{.*?\}```', '', text, flags=re.DOTALL)

        # 移除 media-block
        text = re.sub(r'<media-block>.*?</media-block>', '', text, flags=re.DOTALL)

        return text

    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据，提取热词和内容

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】提取热词和内容 (ExtractHotWordsAndContent)")
        print("="*50)

        # 读取数据文件
        data = self._read_data_file()
        
        # 提取热词和趋势词
        hot_words, trend_word = self._extract_hot_words_and_trend(data)
        
        # 提取内容
        zhisou_content = self._extract_content(data, hot_words)
        
        # 创建输出数据
        output_data, output_file_path = self._create_output_data(hot_words, zhisou_content)
        
        # 更新状态并返回
        return self._update_state(state, trend_word, output_data)
        
    def _read_data_file(self) -> Dict[str, Any]:
        """
        读取数据文件
        
        Returns:
            Dict[str, Any]: 读取的数据
        """
        # 数据文件路径
        data_file_path = os.path.join(self.data_dir, 'data.json')
        print(f"读取数据文件: {data_file_path}")

        # 读取 data.json 文件
        with open(data_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            
        return data
        
    def _extract_hot_words_and_trend(self, data: Dict[str, Any]) -> Tuple[List[str], str]:
        """
        提取热词和趋势词
        
        Args:
            data: 从文件读取的数据
            
        Returns:
            Tuple[List[str], str]: 热词列表和趋势词
        """
        # 提取热词
        hot_words = data.get('hot_word', [])
        trend_word = data.get('trend_word', "")
        
        return hot_words, trend_word
        
    def _extract_content(self, data: Dict[str, Any], hot_words: List[str]) -> Dict[str, str]:
        """
        从数据中提取内容
        
        Args:
            data: 从文件读取的数据
            hot_words: 热词列表
            
        Returns:
            Dict[str, str]: 每个热词对应的内容
        """
        # 初始化知搜内容字典
        zhisou_content = {}

        # 检查数据结构是否包含 formatted_summaries
        if 'formatted_summaries' in data:
            zhisou_content = self._extract_from_formatted_summaries(data)
        else:
            # 如果 formatted_summaries 不存在，使用 smart_search_conclude
            zhisou_content = self._extract_from_smart_search(data, hot_words)
            
        return zhisou_content
        
    def _extract_from_formatted_summaries(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        从formatted_summaries中提取内容
        
        Args:
            data: 从文件读取的数据
            
        Returns:
            Dict[str, str]: 每个热词对应的内容
        """
        zhisou_content = {}
        formatted_summaries = data['formatted_summaries']
        # 从 formatted_summaries 中提取每个热词对应的文本
        for key in formatted_summaries:
            if 'text' in formatted_summaries[key]:
                content = formatted_summaries[key]['text']
                # 清理内容
                zhisou_content[key] = self.clean_content(content)
                
        return zhisou_content
        
    def _extract_from_smart_search(self, data: Dict[str, Any], hot_words: List[str]) -> Dict[str, str]:
        """
        从smart_search_conclude中提取内容
        
        Args:
            data: 从文件读取的数据
            hot_words: 热词列表
            
        Returns:
            Dict[str, str]: 每个热词对应的内容
        """
        zhisou_content = {}
        smart_search_conclude = data.get('smart_search_conclude', {})
        for hot_word in hot_words:
            if hot_word in smart_search_conclude:
                content = smart_search_conclude[hot_word]
                # 清理内容
                zhisou_content[hot_word] = self.clean_content(content)
                
        return zhisou_content
        
    def _create_output_data(self, hot_words: List[str], zhisou_content: Dict[str, str]) -> Tuple[Dict[str, Any], str]:
        """
        创建输出数据和文件路径
        
        Args:
            hot_words: 热词列表
            zhisou_content: 每个热词对应的内容
            
        Returns:
            Tuple[Dict[str, Any], str]: 输出数据和输出文件路径
        """
        # 创建输出数据结构
        output_data = {
            'hot_word': hot_words,
            'zhisou_content': zhisou_content
        }

        print(f"输出数据: {output_data}")

        # 将输出写入新的 JSON 文件
        output_file_path = os.path.join(self.data_dir, 'extract_hot_words_and_content.json')

        with open(output_file_path, 'w', encoding='utf-8') as file:
            json.dump(output_data, file, ensure_ascii=False, indent=2)
            
        print(f"热词数量: {len(hot_words)}")
        print(f"知搜内容条目数: {len(zhisou_content)}")
        print(f"输出文件: {output_file_path}")
        
        return output_data, output_file_path
        
    def _update_state(self, state: Dict[str, Any], trend_word: str, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新状态
        
        Args:
            state: 当前状态
            trend_word: 趋势词
            output_data: 输出数据
            
        Returns:
            Dict[str, Any]: 更新后的状态
        """
        # 更新状态
        state["data"]["trend_word"] = trend_word
        state["data"]["extract_hot_words_and_content"] = output_data
        state["current_step"] = "extract_hot_words_and_content"

        print("="*50)
        print("【完成执行】提取热词和内容 (ExtractHotWordsAndContent)")
        print("="*50 + "\n")

        return state

if __name__ == "__main__":
    extractor = ExtractHotWordsAndContent()
    extractor.process({})