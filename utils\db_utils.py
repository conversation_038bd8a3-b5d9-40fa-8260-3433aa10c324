# -*- coding:utf-8 -*-
import sys
import os
import pymysql
import configparser
from utils.db_pool import DBConnectionPool

# 获取当前文件的绝对路径，并添加上一级目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from conf.config import mysql_video_table


class MySQLDatabase:
    """MySQL数据库操作类"""
    
    def __init__(self, conf_path, read_only=False):
        """
        初始化数据库连接
        
        Args:
            conf_path: 配置文件路径
            read_only: 是否只读模式
        """
        try:
            self.config_path = conf_path
            self.read_only = read_only
            self._load_config()
            self.connection = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port,
                cursorclass=pymysql.cursors.DictCursor
            )
        except pymysql.Error as err:
            print(f"Error connecting to database: {err}")

    def _load_config(self):
        """
        从ini配置文件加载配置
        """
        try:
            config = configparser.ConfigParser()
            config.read(self.config_path, encoding='utf-8')
            # 读取API配置段
            if not self.read_only:
                mysql_section = config["VIDEO_MYSQL"]
            else:
                mysql_section = config["VIDEO_MYSQL_READ_ONLY"]

            # 赋值
            self.host = mysql_section.get('host')
            self.port = mysql_section.getint('port')
            self.user = mysql_section.get('user')
            self.password = mysql_section.get('password')
            self.database = mysql_section.get('database')

        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            raise

    def insert_record(self, table_name, data):
        """
        插入记录
        
        Args:
            table_name: 表名
            data: 数据
            
        Returns:
            插入的ID
        """
        try:
            with self.connection.cursor() as cursor:
                sql = mysql_video_table[table_name]["insert"]
                cursor.execute(sql, data)
                last_id = cursor.lastrowid
            self.connection.commit()
            print(cursor.rowcount, "record inserted.")
            return last_id
        except pymysql.Error as err:
            print(f"Error inserting record: {err}")
            return None

    def query_records(self, table_name, data):
        """
        查询记录
        
        Args:
            table_name: 表名
            data: 查询条件
            
        Returns:
            查询结果
        """
        try:
            with self.connection.cursor() as cursor:
                sql = mysql_video_table[table_name]["query"]
                cursor.execute(sql, data)
                results = cursor.fetchall()
                return results
        except pymysql.Error as err:
            print(f"Error querying records: {err}")
            return []

    def delete_record(self, table_name, data):
        """
        删除记录
        
        Args:
            table_name: 表名
            data: 删除条件
            
        Returns:
            删除结果
        """
        try:
            with self.connection.cursor() as cursor:
                sql = mysql_video_table[table_name]["delete"]
                cursor.execute(sql, data)
            self.connection.commit()
            print(cursor.rowcount, "record(s) deleted.")
            return True
        except pymysql.Error as err:
            print(f"Error deleting record: {err}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("MySQL connection is closed.")


def write_project_video_info(conf_path, nor_word, title, project_config_path="", project_content_type="1", video_build_flag="1"):
    """
    写入项目视频信息
    
    Args:
        conf_path: 配置文件路径
        nor_word: 标准词
        title: 标题
        project_config_path: 项目配置路径
        project_content_type: 视频内容类型
        video_build_flag: 视频构建标志
    Returns:
        插入的ID
    """
    pool = DBConnectionPool(conf_path)
    conn = pool.get_connection()
    cursor = conn.cursor()
    
    try:
        sql = "INSERT INTO project_video_info (nor_word, title, project_config_path, project_content_type, video_build_flag) VALUES (%s, %s, %s, %s, %s)"
        cursor.execute(sql, (nor_word, title, project_config_path, project_content_type, video_build_flag))
        conn.commit()
        last_id = cursor.lastrowid
        print("1 record inserted.")
        return last_id
    except Exception as e:
        print(f"插入记录失败: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()  # 这里的close实际上是将连接返回到连接池


def write_project_video_scene(conf_path, project_id, scene_order, scene_path, scene_caption,
                              script_path, duration_seconds, scene_title, is_enabled=1):
    """
    写入项目视频场景
    
    Args:
        conf_path: 配置文件路径
        project_id: 项目ID
        scene_order: 场景顺序
        scene_path: 场景路径
        scene_caption: 场景标题
        script_path: 脚本路径
        duration_seconds: 持续时间(毫秒)
        is_enabled: 是否启用
        
    Returns:
        插入的ID
    """
    pool = DBConnectionPool(conf_path)
    conn = pool.get_connection()
    cursor = conn.cursor()
    
    try:
        sql = """INSERT INTO project_video_scene 
                (project_id, scene_order, scene_path, scene_caption, script_path, duration_seconds, scene_title, is_enabled) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
        cursor.execute(sql, (project_id, scene_order, scene_path, scene_caption,
                           script_path, duration_seconds, scene_title, is_enabled))
        conn.commit()
        scene_id = cursor.lastrowid
        print("1 record inserted.")
        return scene_id
    except Exception as e:
        print(f"插入记录失败: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()  # 这里的close实际上是将连接返回到连接池

def write_project_video_scene_material(conf_path, project_id, scene_order, material_type, material_path, material_name,
                                  config_json, is_enabled=1):
    """
    写入镜头引入的图片到数据库
    """
    pool = DBConnectionPool(conf_path)
    conn = pool.get_connection()
    cursor = conn.cursor()
    
    try:
        sql = """INSERT INTO project_video_scene_material 
                (project_id, scene_order, material_type, material_path, material_name, config_json, is_enabled) 
                VALUES (%s, %s, %s, %s, %s, %s, %s)"""
        print("write_project_video_scene_material sql:", sql)

        cursor.execute(sql, (project_id, scene_order, material_type, material_path,
                           material_name, config_json, is_enabled))
        conn.commit()
        material_id = cursor.lastrowid
        print("1 record inserted.")
        return material_id
    except Exception as e:
        print(f"插入记录失败: {e}")
        conn.rollback()
        raise
    finally:
        cursor.close()
        conn.close()  # 这里的close实际上是将连接返回到连接池
    

if __name__ == "__main__":
    # 测试代码
    conf_path = "../conf/conf.ini"
    db = MySQLDatabase(conf_path)
    # 查询项目视频信息
    results = db.query_records("project_video_info", ("测试标题",))
    print(results)
    db.close_connection()


