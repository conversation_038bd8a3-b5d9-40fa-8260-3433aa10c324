from typing import Dict, Any, Tuple, List
from langchain_core.messages import BaseMessage

from utils.file_utils import <PERSON>Manager, safe_parse_json
from utils.prompt_utils import PromptManager
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor

class DirectorScriptGenerator:
    """导演脚本生成节点"""

    def __init__(self):
        """初始化导演脚本生成节点"""
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】导演脚本生成 (DirectorScriptGenerator)")
        print("="*50)

        # 获取分析报告和场景列表
        news_report, sections = self._get_news_report_and_sections(state)
        
        # 准备批处理数据
        batch_items, prompt = self._prepare_batch_items(sections)
        
        # 执行批处理生成脚本
        results = await self._generate_scripts_batch(batch_items, prompt)
        
        # 更新分析报告和状态
        self._update_news_report_and_state(state, news_report, sections, results)

        print("="*50)
        print("【完成执行】导演脚本生成 (DirectorScriptGenerator)")
        print("="*50 + "\n")

        return state
        
    def _get_news_report_and_sections(self, state: Dict[str, Any]) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        获取分析报告和场景列表
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[Dict[str, Any], List[Dict[str, Any]]]: 分析报告和场景列表
        """
        # 从 state 中获取分析报告
        news_report = state["data"]["news_report"]
        print("已获取分析报告")

        # 获取场景列表
        sections = news_report.get("sections", [])
        print(f"获取到场景数量: {len(sections)}")
        
        return news_report, sections
        
    def _prepare_batch_items(self, sections: List[Dict[str, Any]]) -> Tuple[List[Tuple[str, Dict[str, Any]]], str]:
        """
        准备批处理数据
        
        Args:
            sections: 场景列表
            
        Returns:
            Tuple[List[Tuple[str, Dict[str, Any]]], str]: 批处理项列表和提示词
        """
        # 获取提示词
        prompt = self.prompt_manager.get_prompt("3")
        print("已加载提示词模板")

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        shot_count = 0
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则为每个子镜头生成脚本
                for j, subsection in enumerate(subsections):
                    shot_count += 1
                    shot_id = f"{section_id}_shot_{j+1}"
                    batch_items.append((shot_id, subsection))
                    print(f"准备处理场景 {section_id} 的镜头 {j+1}")
            else:
                # 如果没有子镜头，则为整个场景生成脚本
                batch_items.append((section_id, section))
                print(f"准备处理场景 {i+1}")

        print(f"准备批处理 {len(batch_items)} 个场景/镜头")
        
        return batch_items, prompt
        
    async def _generate_scripts_batch(self, batch_items: List[Tuple[str, Dict[str, Any]]], prompt: str) -> List[Tuple[str, Dict[str, Any], str]]:
        """
        执行批处理生成脚本
        
        Args:
            batch_items: 批处理项列表
            prompt: 提示词
            
        Returns:
            List[Tuple[str, Dict[str, Any], str]]: 处理结果列表
        """
        # 执行批处理
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=lambda item: self._create_messages_for_scene(item, prompt),
            process_response_func=self._process_scene_response,
            max_retries=5
        )
        
        return results
        
    def _create_messages_for_scene(self, item: Tuple[str, Dict[str, Any]], prompt: str) -> List[BaseMessage]:
        """
        创建场景消息
        
        Args:
            item: 批处理项
            prompt: 提示词
            
        Returns:
            List[BaseMessage]: 消息列表
        """
        shot_id, script = item
        print(f"准备处理: {shot_id}")
        return self.llm.create_messages(
            system_prompt=prompt,
            history=[],  # 每个场景使用空的历史记录
            query=str(script)
        )
        
    def _process_scene_response(self, item: Tuple[str, Dict[str, Any]], response: str) -> Tuple[str, Dict[str, Any], str]:
        """
        处理场景响应
        
        Args:
            item: 批处理项
            response: LLM生成的响应
            
        Returns:
            Tuple[str, Dict[str, Any], str]: 处理结果
        """
        shot_id, script = item
        script_title = script.get("title", f"未获取到标题")

        # 将响应作为section_script
        section_script = response

        # 如果解析失败，使用默认空对象
        if not section_script or len(section_script) == 0:
            section_script = "导演脚本生成失败，请检查输入数据。"

        print(f"已处理 {shot_id} 的导演脚本")
        return shot_id, script, section_script
        
    def _update_news_report_and_state(self, state: Dict[str, Any], news_report: Dict[str, Any], 
                                   sections: List[Dict[str, Any]], results: List[Tuple[str, Dict[str, Any], str]]) -> None:
        """
        更新分析报告和状态
        
        Args:
            state: 当前状态
            news_report: 分析报告
            sections: 场景列表
            results: 处理结果列表
        """
        # 创建一个字典来存储所有脚本结果
        all_scripts = {}

        # 将结果添加到分析报告中
        for shot_id, script, section_script in results:
            all_scripts[shot_id] = section_script
            
        # 更新分析报告中的section_script字段
        self._update_section_scripts(sections, news_report, all_scripts)

        # 更新分析报告（包含了更新后的场景数据）
        state["data"]["news_report"] = news_report
        state["current_step"] = "generate_director_script"

        # 保存所有场景导演脚本到单独的文件
        self.file_manager.write_json("director_scripts.json", all_scripts)

        # 保存完整的状态数据
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存导演脚本到: director_scripts.json")
        
    def _update_section_scripts(self, sections: List[Dict[str, Any]], news_report: Dict[str, Any], 
                             all_scripts: Dict[str, str]) -> None:
        """
        更新分析报告中的section_script字段
        
        Args:
            sections: 场景列表
            news_report: 分析报告
            all_scripts: 所有脚本结果
        """
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则更新每个子镜头的section_script
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    if shot_id in all_scripts:
                        # 更新子镜头的section_script
                        news_report["sections"][i]["subsections"][j]["section_script"] = all_scripts[shot_id]
                        print(f"已更新 {shot_id} 的section_script")
            else:
                # 如果没有子镜头，则更新整个场景的section_script
                if section_id in all_scripts:
                    news_report["sections"][i]["section_script"] = all_scripts[section_id]
                    print(f"已更新 {section_id} 的section_script")


if __name__ == "__main__":
    generator = DirectorScriptGenerator()
    generator.process()

