from typing import Literal, Optional, List, Dict, Any

class Message:
    """消息模型，表示对话中的一条消息"""
    
    def __init__(self, role: Literal["system", "user", "assistant"], content: str):
        """
        初始化消息
        
        Args:
            role: 消息角色，可以是"system"、"user"或"assistant"
            content: 消息内容
        """
        self.role = role
        self.content = content
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典格式的消息
        """
        return {
            "role": self.role,
            "content": self.content
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """
        从字典创建消息
        
        Args:
            data: 字典格式的消息
            
        Returns:
            Message: 消息对象
        """
        return cls(
            role=data["role"],
            content=data["content"]
        )
