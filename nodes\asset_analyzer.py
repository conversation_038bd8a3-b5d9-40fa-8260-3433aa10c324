from typing import Dict, Any, Tuple, List
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager, safe_parse_json
from utils.prompt_utils import PromptManager
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor

class AssetAnalyzer:
    """资产分析节点"""

    def __init__(self):
        """初始化资产分析节点"""
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)

    def extract_json_from_text(self, text: str) -> str:
        """
        从文本中提取JSON部分

        Args:
            text: 包含JSON的文本

        Returns:
            str: 提取的JSON字符串
        """
        import re

        if not text:
            return ""

        # 尝试查找```json和```之间的内容
        json_pattern = r'```json\s*([\s\S]*?)\s*```'
        matches = re.findall(json_pattern, text)

        if matches:
            # 返回第一个匹配的JSON内容
            json_content = matches[0].strip()
            # 修复可能的不完整JSON
            return self._fix_incomplete_json(json_content)

        # 如果没有找到```json标记，尝试查找{开头和}结尾的内容
        if text.strip().startswith('{') and text.strip().endswith('}'):
            return self._fix_incomplete_json(text.strip())

        # 尝试查找第一个{和最后一个}之间的内容
        if '{' in text and '}' in text:
            start = text.find('{')
            end = text.rfind('}') + 1
            return self._fix_incomplete_json(text[start:end])

        # 如果都没找到，返回原始文本
        return text

    def _fix_incomplete_json(self, json_str: str) -> str:
        """
        修复不完整的JSON字符串

        Args:
            json_str: 可能不完整的JSON字符串

        Returns:
            str: 修复后的JSON字符串
        """
        # 检查括号是否平衡
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')

        # 修复不平衡的括号
        fixed_str = json_str

        # 添加缺少的右大括号
        if open_braces > close_braces:
            fixed_str += '}' * (open_braces - close_braces)

        # 添加缺少的右方括号
        if open_brackets > close_brackets:
            fixed_str += ']' * (open_brackets - close_brackets)

        # 处理可能的截断JSON
        if fixed_str.rstrip().endswith(','):
            fixed_str = fixed_str.rstrip(',')

            # 如果最后一个逗号在数组中，添加结束括号
            if open_brackets > close_brackets:
                fixed_str += ']'

            # 如果最后一个逗号在对象中，添加结束大括号
            if open_braces > close_braces:
                fixed_str += '}'

        return fixed_str

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】资产分析 (AssetAnalyzer)")
        print("="*50)

        # 从 state 中获取分析报告
        news_report = state["data"]["news_report"]
        print("已获取分析报告")

        # 获取场景列表
        sections = news_report.get("sections", [])
        print(f"获取到场景数量: {len(sections)}")

        # 获取提示词
        prompt = self.prompt_manager.get_prompt("2")
        print("已加载提示词模板")

        # 重置消息列表
        state["messages"] = []

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景
        for i, section in enumerate(sections):
            batch_items.append((i, section))

        print(f"准备批处理 {len(batch_items)} 个场景")

        # 定义创建消息的函数
        def create_messages_for_scene(item: Tuple[int, Dict[str, Any]]) -> List[BaseMessage]:
            _, section = item
            print(f"准备处理场景: {section.get('title', '未命名场景')}")
            return self.llm.create_messages(
                system_prompt=prompt,
                history=[],  # 每个场景使用空的历史记录
                query=str(section)
            )

        # 定义处理响应的函数
        def process_scene_response(item: Tuple[int, Dict[str, Any]], response: str) -> Tuple[int, Dict[str, Any]]:
            index, section = item
            section_title = section.get("title", f"未获取到标题")

            # 尝试提取JSON部分
            json_content = self.extract_json_from_text(response)

            # 解析响应为JSON
            section_asset = safe_parse_json(json_content)

            # 如果解析失败，使用默认空对象
            if not section_asset or len(section_asset) == 0:
                section_asset = {
                    "section_title": section_title,
                    "message": "场景资产分析失败",
                    "elements": []
                }

            # 确保section_asset包含section_title字段
            if isinstance(section_asset, dict) and "section_title" not in section_asset:
                section_asset["section_title"] = section_title

            print(f"已处理场景 {section_title} 的资产分析")
            return index, section_asset

        # 执行批处理
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=create_messages_for_scene,
            process_response_func=process_scene_response,
            max_retries=5
        )

        # 创建一个列表来存储所有场景的资产分析结果（用于保存到单独的文件）
        all_scenes_assets = []

        # 将结果添加到分析报告中
        for index, section_asset in results:
            # 获取当前section的title作为key
            section_title = news_report["sections"][index].get("title", "未命名场景")
            # 将资产分析结果直接赋值给当前场景的assets字段
            news_report["sections"][index]["assets"] = section_asset
            print(f"已将资产分析结果赋值给场景 {section_title} 的assets字段")

            # 添加到总结果中（用于保存到单独的文件）
            all_scenes_assets.append(section_asset)

        # 更新分析报告（包含了更新后的场景数据）
        state["data"]["news_report"] = news_report
        state["current_step"] = "analyze_assets"

        # 创建一个字典，使用section_title作为key
        scenes_assets_dict = {}
        for index, section_asset in enumerate(all_scenes_assets):
            # 获取对应section的title
            if index < len(news_report["sections"]):
                section_title = news_report["sections"][index].get("title", f"场景_{index}")
                scenes_assets_dict[section_title] = section_asset
            else:
                scenes_assets_dict[f"场景_{index}"] = section_asset

        # 保存所有场景资产到单独的文件
        self.file_manager.write_json("asset_analyzer.json", scenes_assets_dict)

        # 保存完整的状态数据
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存场景资产到: asset_analyzer.json")
        print("="*50)
        print("【完成执行】资产分析 (AssetAnalyzer)")
        print("="*50 + "\n")

        return state


if __name__ == "__main__":
    analyzer = AssetAnalyzer()
    analyzer.process()

