from typing import Dict, Any, List, Optional, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager, safe_parse_json
from utils.prompt_utils import PromptManager
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor

class Analyze5W1H:
    """5W1H分析节点"""

    def __init__(self):
        """初始化5W1H分析节点"""
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)

    def get_hot_words(self, data: Dict[str, Any]) -> List[str]:
        """
        获取热词列表

        Args:
            data: 提取的数据

        Returns:
            List[str]: 热词列表
        """
        return data.get("hot_word", [])

    def get_content_by_hot_word(self, data: Dict[str, Any], hot_word: str) -> Optional[str]:
        """
        根据热词获取对应的内容

        Args:
            data: 提取的数据
            hot_word: 热词

        Returns:
            Optional[str]: 热词对应的内容，如果不存在则返回None
        """
        zhisou_content = data.get("zhisou_content", {})
        return zhisou_content.get(hot_word)

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】5W1H分析 (Analyze5W1H)")
        print("="*50)

        # 读取数据并准备批处理
        zhisou_data, batch_items, prompt = self._prepare_batch_data(state)
        
        # 执行批处理
        results = await self._execute_batch_processing(batch_items, prompt)
        
        # 更新状态并保存结果
        self._update_state_with_results(state, zhisou_data, results)

        print("="*50)
        print("【完成执行】5W1H分析 (Analyze5W1H)")
        print("="*50 + "\n")

        return state
        
    def _prepare_batch_data(self, state: Dict[str, Any]) -> Tuple[Dict[str, Any], List[Tuple[str, str]], str]:
        """
        读取数据并准备批处理
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[Dict[str, Any], List[Tuple[str, str]], str]: 知搜数据、批处理项列表和提示词
        """
        # 读取state中的数据
        zhisou_data = state["data"]["extract_hot_words_and_content"]
        print(f"读取state中的数据: extract_hot_words_and_content.json")

        # 获取热词列表
        hot_words = self.get_hot_words(zhisou_data)
        print(f"获取到热词数量: {len(hot_words)}")

        # 重置消息列表
        state["messages"] = []

        # 准备批处理数据
        batch_items = []
        prompt = self.prompt_manager.get_prompt("0")

        # 收集所有需要处理的热词和内容
        for hot_word in hot_words:
            content = self.get_content_by_hot_word(zhisou_data, hot_word)
            if content:
                batch_items.append((hot_word, content))

        print(f"准备批处理 {len(batch_items)} 个热词")
        
        return zhisou_data, batch_items, prompt
        
    async def _execute_batch_processing(self, batch_items: List[Tuple[str, str]], prompt: str) -> List[Tuple[str, str]]:
        """
        执行批处理
        
        Args:
            batch_items: 批处理项列表
            prompt: 提示词
            
        Returns:
            List[Tuple[str, str]]: 批处理结果列表
        """
        # 执行批处理
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=lambda item: self._create_messages_for_hot_word(item, prompt),
            process_response_func=self._process_hot_word_response,
            max_retries=5
        )
        
        return results
        
    def _create_messages_for_hot_word(self, item: Tuple[str, str], prompt: str) -> List[BaseMessage]:
        """
        创建热词分析的消息
        
        Args:
            item: 批处理项，包含热词和内容
            prompt: 提示词
            
        Returns:
            List[BaseMessage]: 消息列表
        """
        hot_word, content = item
        # 格式化文本，包含主题和内容
        formatted_text = f"""
        主题：{hot_word}

        内容：{content}
        """
        return self.llm.create_messages(
            system_prompt=prompt,
            history=[],  # 每个热词使用空的历史记录
            query=formatted_text
        )
        
    def _process_hot_word_response(self, item: Tuple[str, str], response: str) -> Tuple[str, str]:
        """
        处理热词响应
        
        Args:
            item: 批处理项，包含热词和内容
            response: LLM生成的响应
            
        Returns:
            Tuple[str, str]: 包含(热词, 分析结果)的元组
        """
        hot_word, _ = item
        return hot_word, response
        
    def _update_state_with_results(self, state: Dict[str, Any], zhisou_data: Dict[str, Any], 
                                 results: List[Tuple[str, str]]) -> None:
        """
        更新状态并保存结果
        
        Args:
            state: 当前状态
            zhisou_data: 知搜数据
            results: 批处理结果列表
        """
        # 创建一个字典来存储所有热词的分析结果
        all_analysis_results = {}
        
        # 将结果添加到分析结果字典中
        for hot_word, response in results:
            all_analysis_results[hot_word] = response

        # 更新状态中的数据
        if "data" not in state:
            state["data"] = {}

        if "extracted_data" not in state["data"]:
            state["data"]["extracted_data"] = zhisou_data

        # 添加分析结果到state的data的extracted_data中
        state["data"]["extracted_data"]["analyze_5w1h"] = all_analysis_results

        # 将完整的state data保存到data目录下的新文件
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存完整state data到: data/state_data.json")

        # 更新当前步骤
        state["current_step"] = "analyze_5W1H"

if __name__ == "__main__":
    analyzer = Analyze5W1H()
    analyzer.process({})
