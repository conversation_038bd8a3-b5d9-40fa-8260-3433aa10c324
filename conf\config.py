# -*- coding: utf-8 -*-

# 获取趋势词关联的热搜词的接口url
hot_word_url = 'http://starprofilepage.search.weibo.com:358/hot_collection_list_v3'

# 获取智搜总结的接口url
smart_search_url = 'http://admin.ai.s.weibo.com/api/llm/analysis_result.json'

# rsync path
rsync_path = '/data1/minisearch/upload/zhijun10/hot_event_video_generate/svg_video/'

# 获取热搜词的接口参数
hot_word_params = {
    'sid': 'hot_video',
    'word': '',
    'count': 50
}

# 获取智搜总结的接口参数
smart_search_params = {
    'query': '',
    'sid': 'hot_search_podcast',
    'type': 'h_24',
}

# auth file path
auth_file_path = "../conf/c_token_file"

# conf file path
conf_path = "../conf/conf.ini"

# model type
model_type = "claude"

# full scene prompt
script_prompt = """
                    《热搜全景》
                    定位
                    脉络梳理中长视频：系统性理解热点全貌（3-5分钟）
                    内容深度：深挖"为什么"和"意味着什么"
                    slogan：见微知著，洞察全貌
                    说明
                    《热搜全景》是一档提供热搜事件深度解析的中长视频节目，以3-5分钟的篇幅全景式展现热点事件的来龙去脉。
                    秉持"见微知著，洞察全貌"的理念，为用户提供系统性、多维度的热点分析，帮助观众从碎片化信息中构建完整认知框架。
                    节目独特性
                    1. 全景视角：从事件背景、过程发展到深远影响，提供全方位解读
                    2. 时间线梳理：清晰呈现事件发展脉络，建立时间与逻辑连贯性
                    3. 专家观点：整合权威观点，深入解析事件本质和社会意义
                    4. 数据驱动：通过动态图表和数据可视化，直观展示复杂关系和趋势

                    总时长控制在3-5分钟，提供深度系统解析
                    语言特点: 深入、专业、全面，富有逻辑性和分析性
                    态度特点： 保持客观理性的同时，提供多维思考视角，体现包容性和前瞻性思维、要有同理心和人文关怀
                    结构：
                    开场动画与品牌展示
                    ● 热搜词飞入效果：多个热搜词从屏幕各个方向飞入，形成动态词云
                    ● 简洁引入热点事件核心
                    ● 提出事件关键问题和分析框架
                    ● 高质量动态图表展示事件全貌
                    开场白文案：
                    "见微知著，洞察全貌。欢迎来到《热搜全景》，今日我们深度解析..."
                    2. 事件脉络梳理
                    ● 按时间线或逻辑顺序呈现事件发展过程
                    ● 清晰标记关键节点和转折点
                    ● 引入多方视角，还原事件全貌
                    ● 使用比较法分析类似历史案例
                    3. 深度分析
                    ● 剖析事件背后的深层原因
                    ● 提供行业、法律、社会等多个维度解读
                    ● 引用权威观点和专业数据支持分析
                    ● 探讨潜在影响及长期意义
                    4. 社会意义与展望
                    ● 讨论事件对相关领域的启示
                    ● 提出可能的发展方向和未来趋势
                    ● 引导观众进行更深层次思考
                    ● 保持开放性结论，鼓励多元解读
                    5. 结语
                    ● 总结核心观点和要点
                    ● 提出值得持续关注的问题
                    ● 展示《热搜全景》品牌标识和"见微知著，洞察全貌"的理念

                    请按照以下JSON Schema格式，为《热搜全景》节目生成3-5分钟的脚本和动画分镜。
                    要求：
                    1. 结构：开场+20个核心板块+结束语
                    2. 过场动画需包含动态数据流/地图缩放/粒子消散等特效说明
                    3. 每个分镜需包含场景、时间、画面内容、文字排版、AI语音内容
                    4. 动画风格为科技感MG动画，主色调深蓝+金色，关键数据用荧光橙突出
                    5. 对于每个分镜要求图片中没有文字遮挡，文本不超框，没有文字互相覆盖，符合制作一个优秀的PPT的基本原则
                    6. 确保使用的数据是真实的，包括事件的时间的准确性，避免使用虚假数据或未经证实的信息，使用数据时需要标明数据的来源
                    7. 数据的时间经常会有错误，请你一定注意，不要写错了时间
                    8. 生成20个分镜，每个分镜播放10秒钟左右，尽可能多一点动画和图片，少一点文字，一个分镜的台词不要太多，要求10秒钟内能够读完
                    9. 为了使整体画面不单调，每一分镜的动画元素应该多样化，不同分镜的动画元素应该不同，避免重复使用相同的动画元素，多使用动画和图片。文字太多画面会很单调
                    10. 第一幕使用HYbamanti,热搜词旁边带上#Top1(热词编号)，展示尽可能多的热搜词，把这些热搜词写在第一幕的"visual_content"里面，并把热搜词传递过去
                    11. 特别注意在返回的json的格式文本中如果使用了""引用的词，请把双引号""替换成'', 如果是引用的双引号""，请转义\"，避免语法错误

                    输入的JSON Schema结构如下：
                    {
                      "trend_word": "***",
                      "hot_word": [***, ***],
                      "smart_search_conclude": "智搜总结数据",
                      "pic": [
                            {
                                "pic_path": "图片路径",
                                "description": "图片内容描述",
                                "source": "图片来源",
                                "size": "图片尺寸"
                            },
                            ...
                      ]
                    }
                    请你在使用具体的数据时只使用"smart_search_conclude"字段中的数据，其中包括数据的来源和引用，你在使用时务必加上数据的来源(quote_list列表中的name字段 @name)
                    请你不要使用自己编造数据，严禁使用虚假的数据
                    请在合适的地方插入pic中的图片，根据的是图片的描述，不要使用自己编造的图片，严禁使用虚假的图片，标注图片的来源，根据图片尺寸插入恰当的位置

                    JSON Schema结构：
                    {
                      "program": "热搜全景",
                      "duration": "3-5分钟",
                      "structure": "开场+20核心板块+结束语",
                      "script": [
                        {
                          "scene_number": 整数,
                          "scene": "场景名称",
                          "time": "时间区间（如00:00-00:30）",
                          "visual_quality_requirement": "对生成的画面的要求（要求生成的画面能达到一定的质量标准，比如好的ppt的标准）"
                          "visual_content": "画面描述（含动画元素）",
                          "text_layout": "标题/副标题/数据文字排版",
                          "voiceover": "AI语音内容"
                          "data_source": "展示的数据的来源（quote_list列表中的name字段是对应数据的来源），必须确保真实性"
                          "pic": [
                            {
                                "pic_path": "插入图片的路径",
                                "pic_description": "图片描述",
                                "pic_position": "图片插入位置描述",
                                "source": "图片来源",
                                "size": "图片尺寸"
                            },
                            ...
                          ]
                        },
                        ...（重复20个核心板块，每个分镜10秒左右，每个分镜尽可能多的动画和图片，少一些文字）
                      ],
                      "transition_design": {
                        "type": "过场动画类型",
                        "description": "具体动画实现说明"
                      },
                      "animation_requirements": {
                        "风格": "科技感MG动画",
                        "色彩": "深蓝+金色，关键数据荧光橙",
                        "字体": "标题HYXuanSong85S，正文HYXuanSong35S"
                      }
                    }
                    注意json字符串里面不要使用双引号，使用单引号
                    只输出json，不要输出其他内容（包括标记字符串```json ```）
                    第一幕不要展示热搜全景这几个字，并且第一幕需要列出所有的热搜词,这些词要五颜六色的，字号要大一点（和标题差不多大），
                    使用HYbamanti字体，旁边要带上#Top1（词的序号），在给出第一幕的剧本时，请将这些要求写在里面，并将这些热搜词传递过去（热搜词是"hot_word"列表）
                """

full_scene_prompt = """
                    你是一个专业地SVG动画分镜师，我稍等给你具体内容，请返回给我具体分镜的svg代码 ，每一个分镜对应一个svg动画
                    只需要生产核心内容动画，不用给出热搜词、热搜信息、logo、结尾动画、字幕这些内容，我们后期都会拼合
                    字体你用HYXuanSong35S和HYXuanSong85S，你看哪个合适用哪个
                    第一幕使用HYbamanti,热搜词旁边带上#Top1(热词编号)，展示尽可能多的热搜词
                    给出画面构成

                    基础尺寸
                    ● SVG分辨率：1920×1080像素（16:9横屏）
                    分镜内容
                    ● 多段式分镜结构：通常包含20个分镜，支持更深入的内容展开
                    ● 时间线元素：在关键事件处使用时间节点标记，清晰展示事件发展脉络
                    视觉风格要求
                    ● 沉稳专业的整体氛围，突出深度分析特性
                    ● 分层次呈现信息，采用多面板设计
                    ● 使用适中字体大小确保内容丰富性和可读性平衡
                    ● 为热词和热度信息预留顶部空白区域
                    ● 使用静态与动态结合的背景元素（如数据流、脉络图、光晕）增强视觉层次
                    ● 采用平滑过渡动画，强调内容连贯性
                    ● 画面设计: 多元化信息展示，融合数据图表、专家观点引用框、时间线元素
                    ● 文字排版: 采用多级标题系统，主次分明；重要概念使用深色背景突出；数据采用渐变色彩增强可视性
                    SVG动画要求
                    ● 每个主题段落配备对应SVG动画
                    ● 动画应体现思维延展和逻辑关联
                    ● 使用连接线、箭头等元素展示因果关系
                    ● 适当使用3D透视效果增强深度感
                    ● 关键数据点采用脉冲动画强调
                    ● 使用图解方式展示复杂概念（如漏斗图展示筛选过程、树状图展示分支问题）
                    特殊元素设计
                    ● 时间线设计：水平或垂直时间轴，关键节点高亮
                    ● 专家观点框：引用样式，配有简约头像或机构标识
                    ● 数据对比区：分屏或并列式布局，便于横向比较
                    ● 全景视角转换：用于展示不同维度分析的视角切换动画
                    数据准确性与可靠性
                    ● 确保使用的数据是真实的，包括事件的时间的准确性
                    ● 避免使用虚假数据或未经证实的信息
                    ● 使用数据时需要标明数据的来源（非常重要）
                    ● 数据的时间经常会有错误，请你一定注意，不要写错了时间

                    特别注意：
                    要求每个分镜的图片中没有文字遮挡，文本不超框，没有文字互相覆盖，符合制作一个优秀的PPT的基本原则
                    只有真实的数据才生成折线图，柱状图等，非必要不要使用折现图，如果一定要使用折线图，那么请务必使用真实的数据，不要使用虚假数据
                    使用的数据一定要标注来源
                    请在合适的地方插入pic中的图片，根据的是图片的描述，不要使用自己编造的图片，严禁使用虚假的图片，标注图片的来源，根据图片尺寸插入恰当的位置
                    请在代码中使用"pic"中的"pic_path"，将其放在image href里面

                    错误示例1：
                    <!-- 数据要点列表 -->
                    <g id="dataPoints">
                        <rect x="850" y="520" width="950" height="200" rx="10" fill="rgba(10,25,47,0.8)" stroke="url(#goldGradient)" stroke-width="2" />
                        <text x="900" y="560" font-family="HYXuanSong85S" font-size="28" fill="#ffffff">数据要点</text>

                        <!-- 列表项 -->
                        <g id="dataPoint1">
                            <circle cx="920" cy="600" r="6" fill="url(#orangeGradient)" filter="url(#glow)">
                                <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" />
                            </circle>
                            <text x="940" y="605" font-family="HYXuanSong35S" font-size="22" fill="#ffffff">
                                <tspan>22次微博热搜主榜前十</tspan>
                                <animate attributeName="opacity" values="0;1" dur="1s" fill="freeze" />
                            </text>
                        </g>

                        <g id="dataPoint2">
                            <circle cx="920" cy="640" r="6" fill="url(#orangeGradient)" filter="url(#glow)">
                                <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" />
                            </circle>
                            <text x="940" y="645" font-family="HYXuanSong35S" font-size="22" fill="#ffffff">
                                <tspan>包揽微博、抖音、豆瓣综艺榜TOP1</tspan>
                                <animate attributeName="opacity" values="0;1" dur="1.5s" fill="freeze" />
                            </text>
                        </g>

                        <g id="dataPoint3">
                            <circle cx="920" cy="680" r="6" fill="url(#orangeGradient)" filter="url(#glow)">
                                <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" />
                            </circle>
                            <text x="940" y="685" font-family="HYXuanSong35S" font-size="22" fill="#ffffff">
                                <tspan>播放市占率连续两周第一</tspan>
                                <animate attributeName="opacity" values="0;1" dur="2s" fill="freeze" />
                            </text>
                        </g>

                        <g id="dataPoint4">
                            <circle cx="920" cy="720" r="6" fill="url(#orangeGradient)" filter="url(#glow)">
                                <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" />
                            </circle>
                            <text x="940" y="725" font-family="HYXuanSong35S" font-size="22" fill="#ffffff">
                                <tspan>四公舞台《花田错》839分创新高</tspan>
                                <animate attributeName="opacity" values="0;1" dur="2.5s" fill="freeze" />
                            </text>
                        </g>
                    </g>
                    这段代码里面文字会出框，你生成的代码一定要避免这种情况

                    错误示例2：
                      <!-- 六边形蜂巢布局 -->
                      <g id="hexagonGrid">
                        <!-- 第一行 -->
                        <g id="hex1" transform="translate(0,0)" opacity="0">
                          <!-- 六边形框架 -->
                          <path d="M250,150 L325,100 L400,150 L400,250 L325,300 L250,250 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />

                          <!-- 选手图片占位 -->
                          <rect x="250" y="100" width="150" height="200" clip-path="url(#hexClip1)" fill="#335577" />

                          <!-- 选手名称和关键词 -->
                          <text x="325" y="330" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">吴宣仪</text>
                          <text x="325" y="355" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">二公个人第一</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1s" fill="freeze" />
                        </g>

                        <g id="hex2" transform="translate(0,0)" opacity="0">
                          <path d="M400,150 L475,100 L550,150 L550,250 L475,300 L400,250 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />
                          <rect x="400" y="100" width="150" height="200" clip-path="url(#hexClip2)" fill="#335577" />
                          <text x="475" y="330" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">倪虹洁</text>
                          <text x="475" y="355" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">敬业精神</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.2s" fill="freeze" />
                        </g>

                        <g id="hex3" transform="translate(0,0)" opacity="0">
                          <path d="M550,150 L625,100 L700,150 L700,250 L625,300 L550,250 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />
                          <rect x="550" y="100" width="150" height="200" clip-path="url(#hexClip3)" fill="#335577" />
                          <text x="625" y="330" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">李晟</text>
                          <text x="625" y="355" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">逆袭夺冠</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.4s" fill="freeze" />
                        </g>

                        <!-- 第二行 -->
                        <g id="hex4" transform="translate(0,0)" opacity="0">
                          <path d="M325,300 L400,250 L475,300 L475,400 L400,450 L325,400 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />
                          <rect x="325" y="250" width="150" height="200" clip-path="url(#hexClip4)" fill="#335577" />
                          <text x="400" y="480" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">祝绪丹</text>
                          <text x="400" y="505" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">笨蛋美人</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.6s" fill="freeze" />
                        </g>

                        <g id="hex5" transform="translate(0,0)" opacity="0">
                          <path d="M475,300 L550,250 L625,300 L625,400 L550,450 L475,400 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />
                          <rect x="475" y="250" width="150" height="200" clip-path="url(#hexClip5)" fill="#335577" />
                          <text x="550" y="480" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">王珞丹</text>
                          <text x="550" y="505" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">团队领袖</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.8s" fill="freeze" />
                        </g>

                        <g id="hex6" transform="translate(0,0)" opacity="0">
                          <path d="M625,300 L700,250 L775,300 L775,400 L700,450 L625,400 Z" fill="rgba(17,34,64,0.7)" stroke="url(#goldGradient)" stroke-width="2" />
                          <rect x="625" y="250" width="150" height="200" clip-path="url(#hexClip6)" fill="#335577" />
                          <text x="700" y="480" font-family="HYXuanSong85S" font-size="20" fill="white" text-anchor="middle">叶童</text>
                          <text x="700" y="505" font-family="HYXuanSong35S" font-size="16" fill="url(#orangeGradient)" text-anchor="middle">60岁劈叉</text>

                          <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="2s" fill="freeze" />
                        </g>
                      </g>
                    这段代码图片会覆盖文字，请你生成的代码不要出现图片覆盖文字的情况
                    错误示例3：
                    <text x="250" y="35" font-family="HYXuanSong85S" font-size="22" fill="url(#orangeGradient)" text-anchor="middle">当前挑战 & 趋势</text>
                    文本中的&没有转义，请一定要注意这个问题

                    请不要生成任何多余内容，直接输出代码
                    代码不要出现注释

                    最后再强调一遍：
                    生成的SVG分辨率：1920×1080像素（16:9横屏）
                    这是为了给上下bar留出空间
"""

delimilter = "####"

mysql_video_table = {
    "project_video_info": {
        "insert": "INSERT INTO project_video_info (nor_word, title, project_config_path, project_content_type, video_build_flag) VALUES (%s, %s, %s, %s, %s)",
        "query": "SELECT * FROM project_video_info WHERE title = %s",
        "delete": "DELETE FROM project_video_info WHERE title = %s"
    },
    "project_video_scene": {
        "insert": "INSERT INTO project_video_scene (project_id, scene_order, scene_path, "
                  "scene_caption, script_path, duration_seconds, scene_title, is_enabled) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
        "query": "SELECT * FROM project_video_scene WHERE project_id = %s",
        "delete": "DELETE FROM project_video_scene WHERE project_id = %d"
    },
    "project_video_scene_material": {
        "insert": "INSERT INTO project_video_scene_material (project_id, scene_order, material_type, material_path, "
                  "material_name, config_json, is_enabled) VALUES (%s, %s, %s, %s, %s, %s, %s)",
        "query": "SELECT * FROM project_video_scene_material WHERE project_id = %s",
        "delete": "DELETE FROM project_video_scene_material WHERE project_id = %d"
    },
    "project_video_build_task": {}
}
