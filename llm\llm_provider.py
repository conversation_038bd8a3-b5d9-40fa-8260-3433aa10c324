# -*- coding: utf-8 -*-
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from langchain_core.messages import BaseMessage


class LLMProvider(ABC):
    """
    LLM提供者基础协议类
    所有AI提供者必须实现此接口
    """

    @abstractmethod
    async def generate_response(self, messages: List[BaseMessage], enable_thinking: bool = False, thinking_budget_tokens: int = 2000) -> str:
        """
        异步生成响应的抽象方法

        Args:
            messages: 消息列表
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算

        Returns:
            str: 生成的响应文本，如果调用失败则返回"fail"
        """
        pass

    @abstractmethod
    def create_messages(self,
                       system_prompt: str,
                       history: List[BaseMessage],
                       query: str) -> List[BaseMessage]:
        """
        创建消息列表

        Args:
            system_prompt: 系统提示词
            history: 历史消息
            query: 用户查询

        Returns:
            List[BaseMessage]: 消息列表
        """
        pass
