<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>耳环风波 - 黄杨钿甜事件动画</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #ffffff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
        }

        .animation-container {
            width: 100vw;
            height: 100vh;
            background: #ffffff;
            position: relative;
            overflow: hidden;
            /* 确保容器完全填充浏览器窗口 */
        }

        /* 主标题 */
        .main-title {
            position: absolute;
            top: 9.26vh; /* 100px/1080px = 9.26% */
            left: 50%;
            transform: translateX(-50%);
            font-size: 4.44vw; /* 48px/1080px = 4.44% */
            font-weight: bold;
            color: #000;
            z-index: 10;
            text-shadow: 0.1vw 0.1vw 0.2vw rgba(0,0,0,0.1);
        }

        /* 时间标签 */
        .time-label {
            position: absolute;
            top: 18.52vh; /* 200px/1080px = 18.52% */
            right: 16.67vw; /* 320px/1920px = 16.67% */
            font-size: 2.22vw; /* 24px/1080px = 2.22% */
            color: #000;
            z-index: 5;
            opacity: 0;
            animation: fadeIn 0.5s ease-in-out 0s forwards, fadeOut 0.5s ease-in-out 3.5s forwards;
        }

        /* 人物图片 */
        .character-image {
            position: absolute;
            width: 18.75vw; /* 360px/1920px = 18.75% */
            height: 44.44vh; /* 480px/1080px = 44.44% */
            object-fit: cover;
            border-radius: 0.52vw; /* 10px/1920px = 0.52% */
            box-shadow: 0 0.74vh 2.96vh rgba(0,0,0,0.2);
            z-index: 2;
            left: -25vw; /* -480px/1920px = -25% */
            top: 27.78vh; /* 300px/1080px = 27.78% */
            animation: slideInFromLeft 1s ease-out 0.5s forwards,
                       shrinkAndMoveLeft 1s ease-in-out 3.5s forwards,
                       moveToFinalPosition 1s ease-in-out 11.5s forwards;
        }

        /* 耳环图片 */
        .earring-image {
            position: absolute;
            width: 20.83vw; /* 400px/1920px = 20.83% */
            height: 37.04vh; /* 400px/1080px = 37.04% */
            object-fit: cover;
            border-radius: 0.78vw; /* 15px/1920px = 0.78% */
            box-shadow: 0 1.11vh 4.44vh rgba(0,0,0,0.3);
            z-index: 2;
            right: -20.83vw; /* -400px/1920px = -20.83% */
            top: 31.48vh; /* 340px/1080px = 31.48% */
            animation: slideInFromRight 1s ease-out 3.5s forwards,
                       expandToCenter 1s ease-in-out 7.5s forwards,
                       moveToFinalPositionRight 1s ease-in-out 11.5s forwards;
        }

        /* 耳环描述文字 */
        .earring-description {
            position: absolute;
            bottom: 25.93vh; /* 280px/1080px = 25.93% */
            right: 27.08vw; /* 520px/1920px = 27.08% */
            font-size: 2.59vw; /* 28px/1080px = 2.59% */
            color: #fff;
            background: rgba(0,0,0,0.7);
            padding: 0.93vh 1.04vw; /* 10px 20px */
            border-radius: 0.42vw; /* 8px/1920px = 0.42% */
            z-index: 5;
            opacity: 0;
            animation: fadeIn 0.8s ease-in-out 4.5s forwards, fadeOut 0.5s ease-in-out 7.5s forwards;
        }

        /* 价格显示 */
        .price-display {
            position: absolute;
            top: 27.78vh; /* 300px/1080px = 27.78% */
            left: 50%;
            transform: translateX(-50%);
            font-size: 5.93vw; /* 64px/1080px = 5.93% */
            font-weight: bold;
            color: #ff0000;
            z-index: 8;
            opacity: 0;
            animation: priceCountUp 2s ease-out 8s forwards, priceEmphasize 0.5s ease-in-out 10s forwards;
        }

        /* 回应引用 */
        .response-quote {
            position: absolute;
            top: 23.15vh; /* 250px/1080px = 23.15% */
            left: 50%;
            transform: translateX(-50%);
            font-size: 3.33vw; /* 36px/1080px = 3.33% */
            font-style: italic;
            color: #0066cc;
            z-index: 6;
            opacity: 0;
            width: 0;
            white-space: nowrap;
            overflow: hidden;
            animation: typewriter 2s steps(8) 12s forwards;
        }

        /* 质疑文字 */
        .question-text {
            position: absolute;
            bottom: 16.67vh; /* 180px/1080px = 16.67% */
            left: 50%;
            transform: translateX(-50%);
            font-size: 3.89vw; /* 42px/1080px = 3.89% */
            color: #000;
            z-index: 7;
            opacity: 0;
            animation: fadeInWithScale 1s ease-out 14s forwards;
        }

        /* 问号特效 */
        .question-mark {
            position: absolute;
            bottom: 11.11vh; /* 120px/1080px = 11.11% */
            left: 50%;
            transform: translateX(-50%);
            font-size: 7.41vw; /* 80px/1080px = 7.41% */
            color: #ff6600;
            z-index: 7;
            opacity: 0;
            animation: questionMarkEffect 2s ease-out 15s forwards;
        }

        /* 粒子特效容器 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            opacity: 0;
        }

        /* 光晕特效 */
        .glow-effect {
            position: absolute;
            width: 26.04vw; /* 500px/1920px = 26.04% */
            height: 46.30vh; /* 500px/1080px = 46.30% */
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,215,0,0.3) 0%, transparent 70%);
            z-index: 1;
            opacity: 0;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            animation: glowPulse 3s ease-in-out 8s infinite;
        }

        /* 动画定义 */
        @keyframes fadeIn {
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            to { opacity: 0; }
        }

        @keyframes slideInFromLeft {
            to { left: 40.63vw; } /* 780px/1920px = 40.63% */
        }

        @keyframes slideInFromRight {
            to { right: 39.58vw; } /* 760px/1920px = 39.58% */
        }

        @keyframes shrinkAndMoveLeft {
            to {
                width: 12.5vw; /* 240px/1920px = 12.5% */
                height: 29.63vh; /* 320px/1080px = 29.63% */
                left: 25vw; /* 480px/1920px = 25% */
            }
        }

        @keyframes expandToCenter {
            to {
                width: 31.25vw; /* 600px/1920px = 31.25% */
                height: 55.56vh; /* 600px/1080px = 55.56% */
                right: 34.38vw; /* 660px/1920px = 34.38% */
                top: 22.22vh; /* 240px/1080px = 22.22% */
            }
        }

        @keyframes moveToFinalPosition {
            to {
                left: 30vw; /* 576px/1920px = 30% */
                top: 31.48vh; /* 340px/1080px = 31.48% */
            }
        }

        @keyframes moveToFinalPositionRight {
            to {
                width: 15.63vw; /* 300px/1920px = 15.63% */
                height: 27.78vh; /* 300px/1080px = 27.78% */
                right: 30vw; /* 576px/1920px = 30% */
                top: 36.11vh; /* 390px/1080px = 36.11% */
            }
        }

        @keyframes priceCountUp {
            0% { opacity: 1; }
            100% { opacity: 1; }
        }

        @keyframes priceEmphasize {
            0% { transform: translateX(-50%) scale(1); }
            50% { transform: translateX(-50%) scale(1.2); }
            100% { transform: translateX(-50%) scale(1); }
        }

        @keyframes typewriter {
            to {
                width: 15.63vw; /* 300px/1920px = 15.63% */
                opacity: 1;
            }
        }

        @keyframes fadeInWithScale {
            0% {
                opacity: 0;
                transform: translateX(-50%) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }

        @keyframes questionMarkEffect {
            0% {
                opacity: 0;
                transform: translateX(-50%) scale(0.5) rotate(-10deg);
            }
            50% {
                opacity: 1;
                transform: translateX(-50%) scale(1.2) rotate(5deg);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) scale(1) rotate(0deg);
            }
        }

        @keyframes glowPulse {
            0%, 100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            50% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        /* 粒子动画 */
        @keyframes particleFloat {
            0% {
                opacity: 0;
                transform: translateY(0) scale(0);
            }
            10% {
                opacity: 1;
                transform: translateY(-20px) scale(1);
            }
            90% {
                opacity: 1;
                transform: translateY(-200px) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-220px) scale(0);
            }
        }

        /* 数字计数器效果 */
        .price-counter {
            display: inline-block;
        }

        .price-counter::before {
            content: "¥0";
            animation: countUp 2s ease-out 8s forwards;
        }

        @keyframes countUp {
            0% { content: "¥0"; }
            10% { content: "¥230,000"; }
            20% { content: "¥460,000"; }
            30% { content: "¥690,000"; }
            40% { content: "¥920,000"; }
            50% { content: "¥1,150,000"; }
            60% { content: "¥1,380,000"; }
            70% { content: "¥1,610,000"; }
            80% { content: "¥1,840,000"; }
            90% { content: "¥2,070,000"; }
            100% { content: "¥2,300,000"; }
        }
    </style>
</head>
<body>
    <div class="animation-container">
        <!-- 主标题 -->
        <div class="main-title">耳环风波</div>

        <!-- 时间标签 -->
        <div class="time-label">2025年5月</div>

        <!-- 人物图片 -->
        <img src="data/assets/characters/yhdt.jpg" alt="黄杨钿甜" class="character-image">

        <!-- 耳环图片 -->
        <img src="data/assets/props/yhdt_earring.jpg" alt="GRAFF耳环" class="earring-image">

        <!-- 耳环描述 -->
        <div class="earring-description">格拉夫钻石祖母绿耳环</div>

        <!-- 价格显示 -->
        <div class="price-display">
            <span class="price-counter"></span>
        </div>

        <!-- 回应引用 -->
        <div class="response-quote">'向妈妈借用'</div>

        <!-- 质疑文字 -->
        <div class="question-text">财富来源?</div>

        <!-- 问号特效 -->
        <div class="question-mark">?</div>

        <!-- 光晕特效 -->
        <div class="glow-effect"></div>

        <!-- 粒子特效容器 -->
        <div class="particles" id="particles"></div>
    </div>

    <script>
        // 创建粒子特效
        function createParticles() {
            const particlesContainer = document.getElementById('particles');

            // 在价格显示时创建金色粒子
            setTimeout(() => {
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + 'vw';
                    particle.style.top = Math.random() * 100 + 'vh';
                    particle.style.animation = `particleFloat 3s ease-out ${Math.random() * 2}s infinite`;
                    particlesContainer.appendChild(particle);
                }
            }, 8000);
        }

        // 音频提示（模拟）
        function playAudioCues() {
            // 0-4秒：介绍音频
            setTimeout(() => {
                console.log('🎵 音频：二零二五年五月，黄杨钿甜参加学校成人礼活动时佩戴了一副被鉴定为格拉夫品牌的钻石祖母绿耳环');
            }, 0);

            // 4-8秒：价值估算
            setTimeout(() => {
                console.log('🎵 音频：网友估算其价值高达二百三十万元。');
            }, 4000);

            // 8-12秒：价格震撼
            setTimeout(() => {
                console.log('🎵 音频：当质疑声起，黄杨钿甜回应称耳环为"向妈妈借用"，');
                // 添加价格显示音效
                console.log('💰 音效：叮！');
            }, 8000);

            // 12-18秒：后续争议
            setTimeout(() => {
                console.log('🎵 音频：此回应非但未平息争议，反而引发公众对其家庭财富来源的更多疑问。');
                console.log('❓ 音效：低沉疑问音');
            }, 12000);
        }

        // 添加闪烁效果
        function addSparkleEffect() {
            setTimeout(() => {
                const earringImg = document.querySelector('.earring-image');
                earringImg.style.filter = 'brightness(1.3) contrast(1.2)';

                // 创建闪光点
                for (let i = 0; i < 5; i++) {
                    const sparkle = document.createElement('div');
                    sparkle.style.position = 'absolute';
                    sparkle.style.width = '0.42vw'; /* 8px/1920px = 0.42% */
                    sparkle.style.height = '0.74vh'; /* 8px/1080px = 0.74% */
                    sparkle.style.background = '#fff';
                    sparkle.style.borderRadius = '50%';
                    sparkle.style.boxShadow = '0 0 0.52vw #fff';
                    sparkle.style.left = (62.5 + Math.random() * 15.63) + 'vw'; /* 1200px + 300px range */
                    sparkle.style.top = (37.04 + Math.random() * 27.78) + 'vh'; /* 400px + 300px range */
                    sparkle.style.zIndex = '9';
                    sparkle.style.animation = 'sparkle 1s ease-in-out infinite';
                    document.querySelector('.animation-container').appendChild(sparkle);

                    setTimeout(() => sparkle.remove(), 3000);
                }
            }, 8500);
        }

        // 添加闪烁动画
        const sparkleCSS = `
            @keyframes sparkle {
                0%, 100% { opacity: 0; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
            }
        `;
        const style = document.createElement('style');
        style.textContent = sparkleCSS;
        document.head.appendChild(style);

        // 初始化动画
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            playAudioCues();
            addSparkleEffect();

            // 18秒后重新开始动画
            setTimeout(() => {
                location.reload();
            }, 18000);
        });
    </script>
</body>
</html>
