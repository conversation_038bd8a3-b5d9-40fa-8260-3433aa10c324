# -*- coding: utf-8 -*-
"""
    python-version:3.6

    date:2020/06/15

    author:mingzhe6
"""
import SinaStorage_3x
from SinaStorage_3x.bucket import SCSBucket, ACL, FAST_ACL

# 设置全局accesskey和secretkey
access_key = "AK"
secret_key = "SK"
secure = True  # if True use SSL
SinaStorage_3x.setDefaultAppInfo(access_key, secret_key, secure)

if __name__ == "__main__":
    bucket_name = ''
    key_name = ''
    local_file_path = ''

    metaData = {"aaa": "bbb", "ccc": "ddd", "eee": "fff"}
    '''bucket 操作'''
    s = SCSBucket(bucket_name)
    acl = {}
    acl[ACL.ACL_GROUP_ANONYMOUSE] = [ACL.ACL_READ, ACL.ACL_WRITE, ACL.ACL_READ_ACP, ACL.ACL_WRITE_ACP]
    acl[ACL.ACL_GROUP_CANONICAL] = [ACL.ACL_READ_ACP, ACL.ACL_READ, ACL.ACL_WRITE, ACL.ACL_WRITE_ACP]
    s.put_bucket()  # 创建bucket，内部用户需走提案申请
    s.get_bucket_meta()  # 获取bucket meta信息
    s.get_bucket_acl()  # 获取bucket acl权限控制信息
    s.set_bucket_acl(acl)  # 设置bucket acl权限控制信息,当acl为空字典时，会取消掉所有额外权限，只保留拥有者权限
    s.list_object()  # 列举bucket下的文件
    '''service 操作'''
    s = SCSBucket()  # 此处bucketname为空
    s.list_bucket()  # 列举用户拥有的所有bucket
    '''object 操作'''
    s = SCSBucket(bucket_name)
    s.get_object_meta(key_name)  # 获取指定key的meta信息
    s.put_object(key_name, local_file_path, metaData)  # 上传名为key_name的文件，并携带指定meta信息
    s.get_object_acl(key_name)  # 获取指定key的acl
    s.set_object_acl(key_name, acl)  # 设置指定key的acl
    s.set_object_meta(key_name, metaData)  # 设置指定key的meta信息
    s.head_object_meta(key_name)  # head方式获取key meta信息

    # 下载object
    s.get_object(key_name, local_file_path)
    # copy Objecy
    src = 'bucket/keyname'
    s.copy_object(key_name, src)  # copy文件
    sha1 = ''
    length = '123123'
    s.put_object_relax(key_name, sha1, length)  # 通过指定sha1及文件大小，进行秒传
    s.delete_object(key_name)  # 删除文件
    # 分片上传
    # 分片上传时，一个SCSBucket实例一次只进行一个分片上传任务，否则会引起错误
    s = SCSBucket(bucket_name)
    uploadId = s.init_multipart_upload(key_name)
    s.multipart_upload(key_name, local_file_path, uploadId)
    s.list_parts(key_name, uploadId)
    s.complete_multipart_upload(key_name, uploadId)
    s.delete_object(key_name)
