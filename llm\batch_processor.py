#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
from typing import List, Dict, Any, Callable, TypeVar, Optional
from langchain_core.messages import BaseMessage

from llm.base import LLMProvider

T = TypeVar('T')

class BatchProcessor:
    """批处理工具类，用于并行处理多个LLM请求"""

    def __init__(self, provider: LLMProvider, max_concurrent: int = 10):
        """
        初始化批处理工具

        Args:
            provider: LLM提供者
            max_concurrent: 最大并发数量，默认为10
        """
        self.provider = provider
        self.max_concurrent = max_concurrent

    async def process_batch(self,
                           items: List[T],
                           create_messages_func: Callable[[T], List[BaseMessage]],
                           process_response_func: Callable[[T, str], Any],
                           max_retries: int = 5,
                           enable_thinking: bool = False,
                           thinking_budget_tokens: int = 2000,
                           temperature: float = 1.0) -> List[Any]:
        """
        批量处理多个请求

        Args:
            items: 需要处理的项目列表
            create_messages_func: 创建消息的函数，接收一个项目，返回消息列表
            process_response_func: 处理响应的函数，接收项目和响应文本，返回处理结果
            max_retries: 最大重试次数
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算
            temperature: 温度参数，控制生成文本的随机性，默认为1.0

        Returns:
            List[Any]: 处理结果列表
        """
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(self.max_concurrent)

        # 创建任务列表
        tasks = []
        for item in items:
            task = self._process_item(
                item=item,
                create_messages_func=create_messages_func,
                process_response_func=process_response_func,
                semaphore=semaphore,
                max_retries=max_retries,
                enable_thinking=enable_thinking,
                thinking_budget_tokens=thinking_budget_tokens,
                temperature=temperature
            )
            tasks.append(task)

        # 并行执行所有任务
        results = await asyncio.gather(*tasks)
        return results

    async def _process_item(self,
                           item: T,
                           create_messages_func: Callable[[T], List[BaseMessage]],
                           process_response_func: Callable[[T, str], Any],
                           semaphore: asyncio.Semaphore,
                           max_retries: int = 5,
                           enable_thinking: bool = False,
                           thinking_budget_tokens: int = 2000,
                           temperature: float = 1.0) -> Any:
        """
        处理单个项目

        Args:
            item: 需要处理的项目
            create_messages_func: 创建消息的函数
            process_response_func: 处理响应的函数
            semaphore: 控制并发的信号量
            max_retries: 最大重试次数
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算
            temperature: 温度参数，控制生成文本的随机性，默认为1.0

        Returns:
            Any: 处理结果
        """
        async with semaphore:
            # 创建消息
            messages = create_messages_func(item)

            # 设置重试计数器
            retry_count = 0
            response = ""

            # 重试循环
            while retry_count < max_retries:
                try:
                    # 生成响应
                    response = await self.provider.generate_response(
                        messages=messages,
                        enable_thinking=enable_thinking,
                        thinking_budget_tokens=thinking_budget_tokens,
                        temperature=temperature
                    )
                    print(f"生成响应，第{retry_count + 1}次尝试")

                    # 检查响应是否为fail
                    if response != "fail":
                        print(f"成功生成响应，第{retry_count + 1}次尝试")
                        break

                    print(f"生成响应失败，第{retry_count + 1}次尝试")
                    retry_count += 1

                except Exception as e:
                    print(f"生成过程出错: {e}")
                    retry_count += 1
                    print(f"正在进行第{retry_count + 1}次尝试...")

                # 如果重试失败，等待一秒再试
                if retry_count < max_retries:
                    import time
                    await asyncio.sleep(1)

            # 如果所有重试都失败，使用默认响应
            if retry_count >= max_retries and (response == "fail"):
                print(f"经过{max_retries}次尝试，仍然无法获取有效的响应")
                response = "无法生成有效的响应。请检查输入数据或模型配置。"

            # 处理响应 - 检查process_response_func是否为异步函数
            if asyncio.iscoroutinefunction(process_response_func):
                # 如果是异步函数，则使用await调用
                result = await process_response_func(item, response)
            else:
                # 如果不是异步函数，则直接调用
                result = process_response_func(item, response)
                
            return result
