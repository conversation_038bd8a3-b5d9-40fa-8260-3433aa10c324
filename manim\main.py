from manim import *
import os


class LayoutManager:
    """布局管理器 - 避免元素重叠的智能布局系统"""

    @staticmethod
    def get_safe_position_right_of(left_object, right_object, margin=0.5):
        """计算右侧对象相对于左侧对象的安全位置"""
        left_right_edge = left_object.get_right()[0]
        right_width = right_object.width
        safe_x = left_right_edge + right_width/2 + margin
        return [safe_x, left_object.get_center()[1], 0]

    @staticmethod
    def get_safe_position_left_of(right_object, left_object, margin=0.5):
        """计算左侧对象相对于右侧对象的安全位置"""
        right_left_edge = right_object.get_left()[0]
        left_width = left_object.width
        safe_x = right_left_edge - left_width/2 - margin
        return [safe_x, right_object.get_center()[1], 0]

    @staticmethod
    def get_safe_position_above(bottom_object, top_object, margin=0.3):
        """计算上方对象相对于下方对象的安全位置"""
        bottom_top_edge = bottom_object.get_top()[1]
        top_height = top_object.height
        safe_y = bottom_top_edge + top_height/2 + margin
        return [bottom_object.get_center()[0], safe_y, 0]

    @staticmethod
    def get_safe_position_below(top_object, bottom_object, margin=0.3):
        """计算下方对象相对于上方对象的安全位置"""
        top_bottom_edge = top_object.get_bottom()[1]
        bottom_height = bottom_object.height
        safe_y = top_bottom_edge - bottom_height/2 - margin
        return [top_object.get_center()[0], safe_y, 0]

    @staticmethod
    def check_overlap(obj1, obj2, margin=0.1):
        """检查两个对象是否重叠（包含安全边距）"""
        # 使用get_left, get_right, get_top, get_bottom方法
        left1, right1 = obj1.get_left()[0], obj1.get_right()[0]
        top1, bottom1 = obj1.get_top()[1], obj1.get_bottom()[1]
        left2, right2 = obj2.get_left()[0], obj2.get_right()[0]
        top2, bottom2 = obj2.get_top()[1], obj2.get_bottom()[1]

        # 扩展边界以包含安全边距
        left1_expanded = left1 - margin
        right1_expanded = right1 + margin
        top1_expanded = top1 + margin
        bottom1_expanded = bottom1 - margin

        # 检查是否重叠
        return not (right1_expanded < left2 or
                   left1_expanded > right2 or
                   bottom1_expanded > top2 or
                   top1_expanded < bottom2)

    @staticmethod
    def print_layout_debug(obj, name):
        """打印布局调试信息"""
        center = obj.get_center()
        left, right = obj.get_left()[0], obj.get_right()[0]
        top, bottom = obj.get_top()[1], obj.get_bottom()[1]
        print(f"[布局调试] {name}: 中心=({center[0]:.2f}, {center[1]:.2f}), "
              f"边界=左{left:.2f} 右{right:.2f} 上{top:.2f} 下{bottom:.2f}")

    @staticmethod
    def ensure_screen_bounds(obj, screen_width=14, screen_height=8):
        """确保对象在屏幕边界内"""
        center = obj.get_center()
        left, right = obj.get_left()[0], obj.get_right()[0]
        top, bottom = obj.get_top()[1], obj.get_bottom()[1]

        # 检查并调整X坐标
        if left < -screen_width/2:
            center[0] = -screen_width/2 + obj.width/2
        elif right > screen_width/2:
            center[0] = screen_width/2 - obj.width/2

        # 检查并调整Y坐标
        if bottom < -screen_height/2:
            center[1] = -screen_height/2 + obj.height/2
        elif top > screen_height/2:
            center[1] = screen_height/2 - obj.height/2

        obj.move_to(center)
        return obj


class CreateCircle(Scene):
    def construct(self):
        """
        黄杨钿甜成人礼GRAFF耳环事件动画
        总时长：16秒，严格按照动画设计文档实现
        画布尺寸：1920×1080像素 (16:9)
        """
        # 设置画布背景为白色
        self.camera.background_color = WHITE

        # ========== 图片资源路径定义 ==========
        # 严格按照设计文档中的资产路径
        yang_image_path = "../data/assets/characters/yhdt.jpg"  # 黄杨钿甜.jpg
        earring_image_path = "../data/assets/props/yhdt_earring.jpg"  # 黄杨钿甜佩戴的graff耳环.jpg

        # 检查文件是否存在
        if not os.path.exists(yang_image_path):
            print(f"警告：找不到图片文件 {yang_image_path}")
        if not os.path.exists(earring_image_path):
            print(f"警告：找不到图片文件 {earring_image_path}")

        # ========== 创建主要图片元素 ==========

        # 黄杨钿甜照片 - 宽高比1:1，计算合适尺寸
        yang_photo = ImageMobject(yang_image_path)
        yang_photo.height = 4.5  # 设置高度
        yang_photo.width = yang_photo.height  # 保持1:1比例
        yang_photo.move_to(LEFT * 3 + ORIGIN)  # 初始位置在左侧2/3区域
        yang_photo.set_opacity(0)  # 初始透明
        yang_photo.scale(0.9)  # 初始缩放0.9，用于后续放大效果

        # GRAFF耳环 - 宽高比1:1，计算合适尺寸
        earring = ImageMobject(earring_image_path)
        earring.height = 3.0  # 设置高度
        earring.width = earring.height  # 保持1:1比例
        earring.move_to(RIGHT * 8 + ORIGIN)  # 初始位置在右侧屏幕外
        earring.set_opacity(0)  # 初始透明

        # ========== 创建文字元素 ==========

        # 开场文字 "黄杨钿甜成人礼佩戴GRAFF耳环"
        title_text = Text("黄杨钿甜成人礼佩戴GRAFF耳环",
                         font_size=28, color=BLACK, font="SimHei")
        # 使用布局管理器：计算照片右侧的安全位置
        title_safe_pos = LayoutManager.get_safe_position_right_of(yang_photo, title_text, margin=0.8)
        title_text.move_to(title_safe_pos)
        LayoutManager.ensure_screen_bounds(title_text)  # 确保在屏幕边界内
        title_text.set_opacity(0)  # 初始透明
        LayoutManager.print_layout_debug(title_text, "开场标题")

        # 价格文字前缀 "市场价值约" - 避免与耳环重叠
        price_prefix = Text("市场价值约",
                           font_size=24, color=BLACK, font="SimHei")
        # 使用布局管理器：计算耳环上方的安全位置
        # 先将耳环移动到中央位置以便计算
        earring_center_temp = earring.copy()
        earring_center_temp.move_to(ORIGIN)
        price_prefix_safe_pos = LayoutManager.get_safe_position_above(earring_center_temp, price_prefix, margin=0.8)
        price_prefix.move_to(price_prefix_safe_pos)
        LayoutManager.ensure_screen_bounds(price_prefix)  # 确保在屏幕边界内
        price_prefix.set_opacity(0)  # 初始透明
        LayoutManager.print_layout_debug(price_prefix, "价格前缀")

        # 价格数字 "230万元" - 红色高亮，放在价格前缀下方
        price_number = Text("230万元",
                           font_size=36, color=RED, weight=BOLD, font="SimHei")
        price_number_safe_pos = LayoutManager.get_safe_position_below(price_prefix, price_number, margin=0.2)
        price_number.move_to(price_number_safe_pos)
        LayoutManager.ensure_screen_bounds(price_number)  # 确保在屏幕边界内
        price_number.set_opacity(0)  # 初始透明
        price_number.scale(0.5)  # 初始缩放0.5，用于放大效果
        LayoutManager.print_layout_debug(price_number, "价格数字")

        # 回应文字 "回应: '是妈妈的'" - 实现打字机效果，避免与分屏图片重叠
        response_full_text = "回应: '是妈妈的'"
        response_text = Text(response_full_text,
                            font_size=30, color=BLACK, font="SimHei")
        # 计算分屏图片的下边界，文字放在下方安全区域
        response_safe_y = -2.5  # 固定在屏幕下方安全位置
        response_text.move_to([0, response_safe_y, 0])  # 位置在分屏图片下方
        response_text.set_opacity(0)  # 初始透明

        # 为打字机效果创建单个字符
        response_chars = []
        for i, char in enumerate(response_full_text):
            char_text = Text(char, font_size=30, color=BLACK, font="SimHei")
            # 计算每个字符的位置 - 基于安全Y坐标
            char_x = (i - len(response_full_text)/2 + 0.5) * 0.35
            char_text.move_to([char_x, response_safe_y, 0])
            char_text.set_opacity(0)  # 初始透明
            response_chars.append(char_text)
            self.add(char_text)  # 添加到场景中

        # 结论文字 "引发更多质疑" - 放在屏幕中央，背景模糊时不会重叠
        conclusion_text = Text("引发更多质疑",
                              font_size=32, color=BLACK, weight=BOLD, font="SimHei")
        conclusion_text.move_to(ORIGIN)  # 位置在中央（背景模糊时安全）
        conclusion_text.set_opacity(0)  # 初始透明
        conclusion_text.scale(0.8)  # 初始缩放0.8，用于放大效果

        # 垂直分隔线
        separator_line = Line(UP * 3, DOWN * 3, color=BLACK, stroke_width=2)
        separator_line.move_to(ORIGIN)
        separator_line.set_opacity(0)

        # ========== 动画时间线实现 ==========

        # 第一阶段 (0s-5s): 黄杨钿甜开场 + 开场文字
        # 黄杨钿甜从左侧缓慢淡入，占据画面左侧2/3区域，并有轻微放大效果
        self.play(
            yang_photo.animate.set_opacity(1).scale(1/0.9),  # 淡入并从0.9缩放到1.0
            title_text.animate.set_opacity(1),  # 开场文字淡入
            run_time=2.0,
            rate_func=smooth
        )

        # 保持开场状态3秒
        self.wait(3.0)

        # 第二阶段 (5s-10s): 转场到耳环展示 + 价格信息
        # 黄杨钿甜向左滑出画面
        self.play(
            yang_photo.animate.move_to(LEFT * 12).set_opacity(0),
            title_text.animate.set_opacity(0),
            run_time=1.0,
            rate_func=rush_into
        )

        # GRAFF耳环从右侧进入占据画面中央位置，应用60度缓慢旋转效果
        self.play(
            earring.animate.move_to(ORIGIN).set_opacity(1),
            run_time=1.0,
            rate_func=smooth
        )

        # 价格前缀文字先出现在上方
        self.play(
            price_prefix.animate.set_opacity(1),
            run_time=0.5
        )

        # 价格数字以放大+高亮红色效果强调出现
        self.play(
            price_number.animate.set_opacity(1).scale(2.0),  # 从0.5缩放到1.0
            run_time=0.8,
            rate_func=there_and_back_with_pause
        )

        # 耳环60度缓慢旋转展示细节
        self.play(
            earring.animate.rotate(PI/3),  # 60度旋转
            run_time=2.7
        )

        # 第三阶段 (10s-14s): 分屏效果 + 回应文字
        # 清除价格文字
        self.play(
            price_prefix.animate.set_opacity(0),
            price_number.animate.set_opacity(0),
            run_time=0.5
        )

        # 创建分屏效果的小图片
        # 黄杨钿甜小图 - 左侧
        yang_photo_small = ImageMobject(yang_image_path)
        yang_photo_small.height = 3.0  # 缩小尺寸
        yang_photo_small.width = yang_photo_small.height  # 保持1:1比例
        yang_photo_small.move_to(LEFT * 2.5)  # 位置在左侧
        yang_photo_small.set_opacity(0)

        # 耳环移动到右侧并缩小
        earring_small = ImageMobject(earring_image_path)
        earring_small.height = 2.0  # 缩小尺寸
        earring_small.width = earring_small.height  # 保持1:1比例
        earring_small.move_to(RIGHT * 2.5)  # 位置在右侧
        earring_small.set_opacity(0)
        earring_small.rotate(PI/3)  # 保持旋转角度

        # 分屏转场动画
        self.play(
            # 原耳环淡出
            earring.animate.set_opacity(0),
            # 小图淡入
            yang_photo_small.animate.set_opacity(1),
            earring_small.animate.set_opacity(1),
            run_time=1.0,
            rate_func=smooth
        )

        # 垂直分隔线从中央生长
        separator_line_start = Line(ORIGIN, ORIGIN, color=BLACK, stroke_width=2)
        separator_line_end = Line(UP * 3, DOWN * 3, color=BLACK, stroke_width=2)
        separator_line_start.move_to(ORIGIN)
        separator_line_end.move_to(ORIGIN)

        self.play(
            Transform(separator_line_start, separator_line_end),
            run_time=0.5,
            rate_func=smooth
        )

        # 回应文字打字机效果 - 逐字显示
        self.wait(0.5)  # 短暂停顿

        # 逐字显示打字机效果
        for i, char_text in enumerate(response_chars):
            self.play(
                char_text.animate.set_opacity(1),
                run_time=0.12
            )
            if i < len(response_chars) - 1:
                self.wait(0.03)  # 字符间短暂停顿

        # 保持回应文字显示
        self.wait(1.0)

        # 第四阶段 (14s-16s): 背景模糊效果 + 结论文字
        # 清除分屏元素
        self.play(
            FadeOut(yang_photo_small),
            FadeOut(earring_small),
            FadeOut(separator_line_start),
            *[FadeOut(char) for char in response_chars],
            run_time=0.5
        )

        # 黄杨钿甜作为模糊背景铺满整个画面
        yang_background = ImageMobject(yang_image_path)
        yang_background.height = 8.0  # 放大到充满屏幕
        yang_background.width = yang_background.height  # 保持1:1比例
        yang_background.move_to(ORIGIN)  # 移动到中央
        yang_background.set_opacity(0.2)  # 透明度0.2，模拟模糊效果

        self.play(
            FadeIn(yang_background),
            run_time=1.0,
            rate_func=smooth
        )

        # 结论文字放大淡入效果
        self.play(
            conclusion_text.animate.set_opacity(1).scale(1/0.8),  # 从0.8缩放到1.0
            run_time=1.0,
            rate_func=smooth
        )

        # 保持最终画面
        self.wait(1.0)