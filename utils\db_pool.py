import mysql.connector
from mysql.connector import pooling
import configparser
import os

class DBConnectionPool:
    _instance = None
    _pool = None
    
    def __new__(cls, conf_path="../conf/conf.ini"):
        if cls._instance is None:
            cls._instance = super(DBConnectionPool, cls).__new__(cls)
            cls._instance._initialize_pool(conf_path)
        return cls._instance
    
    def _initialize_pool(self, conf_path):
        """初始化连接池"""
        if self._pool is None:
            # 检查配置文件是否存在
            if not os.path.exists(conf_path):
                raise FileNotFoundError(f"配置文件不存在: {conf_path}")
                
            config = configparser.ConfigParser()
            config.read(conf_path, encoding='utf-8')
            
            # 检查必要的配置段是否存在
            if 'VIDEO_MYSQL' not in config:
                raise configparser.NoSectionError(f"配置文件中缺少 'VIDEO_MYSQL' 段")
            
            try:
                db_config = {
                    'host': config.get('VIDEO_MYSQL', 'host'),
                    'user': config.get('VIDEO_MYSQL', 'user'),
                    'password': config.get('VIDEO_MYSQL', 'password'),
                    'database': config.get('VIDEO_MYSQL', 'database'),
                    'port': config.getint('VIDEO_MYSQL', 'port'),
                    'pool_name': 'mypool',
                    'pool_size': 5,  # 连接池大小
                    'pool_reset_session': True
                }
                
                print(f"正在连接数据库: {db_config['host']}:{db_config['port']}")
                self._pool = mysql.connector.pooling.MySQLConnectionPool(**db_config)
                print("数据库连接池初始化成功")
                
            except configparser.Error as e:
                print(f"读取配置文件失败: {e}")
                raise
            except Exception as e:
                print(f"初始化连接池失败: {e}")
                raise
    
    def get_connection(self):
        """获取连接池中的连接"""
        if self._pool is None:
            raise Exception("连接池未初始化")
        try:
            return self._pool.get_connection()
        except Exception as e:
            print(f"获取数据库连接失败: {e}")
            raise
    
    def close_all(self):
        """关闭所有连接（在程序结束时调用）"""
        if self._pool:
            # 这里实际上不需要显式关闭，因为连接会自动返回到池中
            pass 