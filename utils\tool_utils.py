import re
from typing import List, Dict, Any, Optional


class ImageParser:
    """用于解析LLM输出中的图片XML标签的工具类"""
    
    @staticmethod
    def parse_images_from_response(response: str) -> List[Dict[str, str]]:
        """
        从LLM响应中解析出所有的图片信息
        
        Args:
            response: LLM的响应文本，可能包含多个<Image>标签
            
        Returns:
            List[Dict[str, str]]: 解析出的图片信息列表，每个图片包含名称、类型、描述、宽高比、图片格式和路径
        """
        images = []
        
        # 使用正则表达式匹配所有<Image>标签
        image_pattern = re.compile(r'<Image>(.*?)</Image>', re.DOTALL)
        image_matches = image_pattern.findall(response)
        
        for image_content in image_matches:
            image_data = {}
            
            # 解析图片名称
            name_match = re.search(r'<Name>(.*?)</Name>', image_content)
            if name_match:
                image_data['name'] = name_match.group(1).strip()
            
            # 解析图片类型
            type_match = re.search(r'<Type>(.*?)</Type>', image_content)
            if type_match:
                image_data['type'] = type_match.group(1).strip()
            
            # 解析图片描述
            desc_match = re.search(r'<Description>(.*?)</Description>', image_content)
            if desc_match:
                image_data['description'] = desc_match.group(1).strip()
            
            # 解析宽高比
            ratio_match = re.search(r'<AspectRatio>(.*?)</AspectRatio>', image_content)
            if ratio_match:
                image_data['aspect_ratio'] = ratio_match.group(1).strip()
            
            # 解析图片格式
            img_type_match = re.search(r'<ImageType>(.*?)</ImageType>', image_content)
            if img_type_match:
                image_data['image_type'] = img_type_match.group(1).strip()
            
            # 解析图片路径
            path_match = re.search(r'<Path>(.*?)</Path>', image_content)
            if path_match:
                image_data['path'] = path_match.group(1).strip()
            
            images.append(image_data)
        
        return images


if __name__ == "__main__":
    response = """啊实打实，asda打。阿斯顿
    ```
    <Image>
        <Name>image1</Name>
        <Type>image</Type>
        <Description>This is a test image</Description>
        <AspectRatio>16:9</AspectRatio>
        <ImageType>jpg</ImageType>
        <Path>images/image1.jpg</Path>
    </Image>
    ```asd,实打实
    """
    images = ImageParser.parse_images_from_response(response)
    print(images)
