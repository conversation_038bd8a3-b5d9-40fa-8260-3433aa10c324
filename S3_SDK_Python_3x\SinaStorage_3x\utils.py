import urllib.parse
import datetime
import time
import os
import mimetypes
import hashlib
from calendar import timegm
from io import FileIO

def aws_urlquote(value):
    if isinstance(value, str):
        value = value.encode("utf-8")
    return urllib.parse.quote(value, "/")


def aws_md5(data):
    hasher = hashlib.new("sha1")
    if hasattr(data, "read"):
        data.seek(0)
        while True:
            chunk = data.read(8192)
            if not chunk:
                break
            hasher.update(chunk)
        data.seek(0)
    else:
        if isinstance(data, str):
            data = bytes(data, "utf-8")
        hasher.update(data)
    return hasher.hexdigest()


def getSize(filename):
    st = os.stat(filename)
    return st.st_size

def guess_mimetype(fn, default="application/octet-stream"):
    if "." not in fn:
        return default
    bfn, ext = fn.lower().rsplit(".", 1)
    if ext == "jpg":
        ext = "jpeg"
    try:
        content_type = mimetypes.guess_type(bfn + "."+ ext)[0]
        return content_type if content_type is not None else default
    except Exception as e:
        return default

def headers_metadata(headers):
    return dict((h[11:], v) for h, v in headers.items()
                            if h.lower().startswith("x-amz-meta-"))

def metadata_headers(metadata):
    return dict(("X-AMZ-Meta-" + h, v) for h, v in metadata.items())

def rfc822_parsedate(v):
    from email.utils import parsedate
    return datetime.datetime.fromtimestamp(time.mktime(parsedate(v)))

def rfc822_fmtdate(t=None):
    from email.utils import formatdate
    if t is None:
        t = datetime.datetime.utcnow()
    return formatdate(timegm(t.timetuple()), usegmt=True)

def expire2Datetime(expire, base=None):
    if hasattr(time, "timetuple"):
        return expire
    if base is None:
        base = datetime.datetime.now()
    try:
        return base + expire
    except TypeError:
        unix_eighties = 315529200
        if expire < unix_eighties:
            return base + datetime.timedelta(seconds=expire)
        else:
            return datetime.datetime.fromtimestamp(expire)


def info_dict(headers):
    headers = dict((str(k.lower()), str(v))for(k, v) in headers.items()if headers and hasattr(headers, 'items'))
    rv = {"headers": headers, "metadata": headers_metadata(headers)}
    if "content-length" in headers:
        rv["size"] = int(headers["content-length"])
    if "content-type" in headers:
        rv["mimetype"] = headers["content-type"]
    if "date" in headers:
        rv["date"] = rfc822_parsedate(headers["date"])
    if "last-modified" in headers:
        rv["modify"] = rfc822_parsedate(headers["last-modified"])
    return rv


def make_preamb(method, headers):
    lines = (method,
             headers.get("s-sina-sha1", ""),
             headers.get("Content-Type", ""),
             headers.get("Date", ""))
    preamb = "\n".join(str(line) for line in lines) + "\n"
    return preamb


def _res_canonicalize(bucketName=None, objectName=None, subresource=None):
    res = "/"
    if bucketName:
        res += '%s/' % aws_urlquote(bucketName)
    if objectName:
        res += '%s' % aws_urlquote(objectName)
    if subresource:
        res += "?"
        res += subresource
    return res


def _amz_canonicalize(headers):
    rv = {}
    for header, value in headers.items():
        header = header.lower()
        if header.startswith("x-amz-") or header.startswith("x-sina-"):
            rv.setdefault(header, []).append(value)
    parts = []
    for key in sorted(rv):
        parts.append("%s:%s\n" % (key, ",".join(rv[key])))
    return "".join(parts)


class FileWithCallback(FileIO):
    def __init__(self, name, mode='r', callback=None, cb_args=(), cb_kwargs={}):
        self._callback = callback
        self._cb_args = cb_args
        self._cb_kwargs = cb_kwargs
        self._progress = 0
        super(FileWithCallback, self).__init__(name, mode, closefd=True)
        self.seek(0, os.SEEK_END)
        self._len = self.tell()
        self.seek(0)

    def __enter__(self):
        return self

    def __exit__(self, type, value, trace):
        self.close()

    def __len__(self):
        return self._len

    def read(self, n=-1):
        chunk = super(FileWithCallback, self).read(n)
        self._progress += int(len(chunk))
        self._cb_kwargs.update({'size': self._len, 'progress': self._progress})
        if self._callback:
            try:
                self._callback(*self._cb_args, **self._cb_kwargs)
            except:
                from SinaStorage_3x.bucket import ManualCancel
                raise ManualCancel('operation abort')
        return chunk
