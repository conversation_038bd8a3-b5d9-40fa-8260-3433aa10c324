__version__ = "1.1.7"
sd_version = "python 3.6"


class AppInfo(object):
    def __init__(self, access_key, secret_key, secure):
        self.access_key = access_key
        self.secret_key = secret_key
        self.secure = secure


def getDefaultAppInfo(self):
    pass


def setDefaultAppInfo(access_key, secret_key, secure=False):
    default = AppInfo(access_key, secret_key, secure)
    global getDefaultAppInfo
    getDefaultAppInfo = lambda: default
