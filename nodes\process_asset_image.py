#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import hashlib
from typing import Dict, Any, List, Optional

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from llm.claude_local import ClaudeLocalProvider

class ProcessAssetImage:
    """资产图片处理节点"""

    def __init__(self):
        """初始化资产图片处理节点"""
        self.file_manager = FileManager()

        # 初始化S3工具类
        self.conf_path = "conf/conf.ini"
        self.s3_handler = SVGSaveS3(self.conf_path)

        # 创建资产目录结构
        self.asset_base_dir = os.path.join("data", "asset")
        self.character_dir = os.path.join(self.asset_base_dir, "characters")
        self.prop_dir = os.path.join(self.asset_base_dir, "props")

        # 项目哈希值，用于构建S3路径
        self.project_hash = None

        # 确保目录存在
        os.makedirs(self.character_dir, exist_ok=True)
        os.makedirs(self.prop_dir, exist_ok=True)

    def _process_assets(self, assets: List[Dict[str, Any]], asset_type: str) -> List[Dict[str, Any]]:
        """
        处理资产列表，设置正确的路径并上传到S3

        Args:
            assets: 资产列表
            asset_type: 资产类型，'character'或'prop'

        Returns:
            List[Dict[str, Any]]: 处理后的资产列表
        """
        if not assets:
            return []

        if not self.project_hash:
            print("警告: project_hash未设置，无法构建正确的S3路径")
            return assets

        for asset in assets:
            # 获取资产名称和图片类型
            name = asset.get("name", "unknown")
            image_type = asset.get("image_type", "jpg")
            
            # 获取实际文件路径
            local_file_path = os.path.join('data', 'assets', f'image.{image_type.lower()}')

            # 将名称转换为文件名格式（去除空格和特殊字符）
            safe_name = name.replace(" ", "_").replace("/", "_").replace("\\", "_").lower()
            
            # 设置S3文件路径
            s3_file_path = f"svg_video/{self.project_hash}/{safe_name}.{image_type.lower()}"

            # 检查本地文件是否存在
            if os.path.exists(local_file_path):
                try:
                    # 上传文件到S3
                    print(f"正在上传资产 {name} 到S3...")
                    self.s3_handler.put_object(s3_file_path, local_file_path)

                    # 获取S3文件URL并设置为资产路径
                    s3_url = self.s3_handler.get_file_url(s3_file_path)
                    file_path = s3_file_path.split('/')[-1]
                    print(f"文件路径========================: {file_path}")
                    asset["path"] = file_path 
                    asset["s3_url"] = s3_url
                    print(f"资产 {name} 已上传到S3，路径: {s3_file_path}")
                except Exception as e:
                    print(f"上传资产 {name} 到S3失败: {e}")
            else:
                print(f"警告: 本地文件 {local_file_path} 不存在，无法上传到S3")
                # 仍然更新path为只包含文件名
                file_name = os.path.basename(local_file_path)
                asset["path"] = file_name
                print(f"已更新资产 {name} 的路径为: {file_name}")

        return assets

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】资产图片处理 (ProcessAssetImage)")
        print("="*50)

        # 设置项目哈希值
        self._set_project_hash(state)
        
        # 处理所有场景资产
        news_report = self._process_scene_assets(state)
        
        # 更新状态并保存
        self._update_and_save_state(state, news_report)

        print("="*50)
        print("【完成执行】资产图片处理 (ProcessAssetImage)")
        print("="*50 + "\n")

        return state
        
    def _set_project_hash(self, state: Dict[str, Any]) -> None:
        """
        设置项目哈希值
        
        Args:
            state: 当前状态
        """
        # 从state中获取project_hash
        trend_word = state["data"].get("trend_word", "")
        if trend_word:
            self.project_hash = hashlib.md5(trend_word.encode()).hexdigest()
            print(f"已设置project_hash: {self.project_hash}")
        else:
            print("警告: 无法获取trend_word，将使用默认路径")
            
    def _process_scene_assets(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理所有场景资产
        
        Args:
            state: 当前状态
            
        Returns:
            Dict[str, Any]: 更新后的新闻报告
        """
        # 从state中获取分析报告
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        print(f"获取到场景数量: {len(sections)}")

        # 遍历所有场次
        for i, section in enumerate(sections):
            title = section.get("title", "未命名场景")
            print(f"处理场景: {title}")
            
            # 获取场景中的分镜
            subsections = section.get("subsections", [])
            print(f"获取到分镜数量: {len(subsections)}")
            
            # 遍历所有分镜
            for j, subsection in enumerate(subsections):
                self._process_single_subsection(news_report, i, j, subsection)
            
        return news_report
        
    def _process_single_subsection(self, news_report: Dict[str, Any], section_index: int, subsection_index: int, subsection: Dict[str, Any]) -> None:
        """
        处理单个分镜的资产
        
        Args:
            news_report: 新闻报告
            section_index: 场景索引
            subsection_index: 分镜索引
            subsection: 分镜数据
        """
        title = subsection.get("title", "未命名分镜")
        print(f"处理分镜: {title}")

        # 获取分镜资产
        assets = subsection.get("assets", {})
        print(f"获取分镜资产，数量: {len(assets)}")

        # 处理人物资产
        character_assets = assets.get("characterAssets", [])
        print(f"处理人物资产，数量: {len(character_assets)}")
        if character_assets:
            processed_character_assets = self._process_assets(character_assets, "character")
            # 更新分镜中的人物资产
            news_report["sections"][section_index]["subsections"][subsection_index]["assets"]["characterAssets"] = processed_character_assets

        # 处理道具资产
        prop_assets = assets.get("propAssets", [])
        print(f"处理道具资产，数量: {len(prop_assets)}")
        if prop_assets:
            processed_prop_assets = self._process_assets(prop_assets, "prop")
            # 更新分镜中的道具资产
            news_report["sections"][section_index]["subsections"][subsection_index]["assets"]["propAssets"] = processed_prop_assets
            
    def _process_single_scene(self, news_report: Dict[str, Any], scene_index: int, section: Dict[str, Any]) -> None:
        """
        处理单个场景的资产（已废弃，使用_process_single_subsection替代）
        
        Args:
            news_report: 新闻报告
            scene_index: 场景索引
            section: 场景数据
        """
        # 该方法已废弃，保留是为了向后兼容
        pass
        
    def _process_character_assets(self, news_report: Dict[str, Any], scene_index: int, assets: Dict[str, Any]) -> None:
        """
        处理人物资产（已废弃，使用_process_single_subsection替代）
        
        Args:
            news_report: 新闻报告
            scene_index: 场景索引
            assets: 资产数据
        """
        # 该方法已废弃，保留是为了向后兼容
        pass
            
    def _process_prop_assets(self, news_report: Dict[str, Any], scene_index: int, assets: Dict[str, Any]) -> None:
        """
        处理道具资产（已废弃，使用_process_single_subsection替代）
        
        Args:
            news_report: 新闻报告
            scene_index: 场景索引
            assets: 资产数据
        """
        # 该方法已废弃，保留是为了向后兼容
        pass
        
    def _update_and_save_state(self, state: Dict[str, Any], news_report: Dict[str, Any]) -> None:
        """
        更新状态并保存
        
        Args:
            state: 当前状态
            news_report: 新闻报告
        """
        import json
        import tempfile
        from datetime import datetime
        
        # 更新state中的分析报告
        state["data"]["news_report"] = news_report
        state["current_step"] = "process_asset_image"

        # 保存完整的状态数据到本地
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存带有资产路径的状态数据到: state_data.json")
        
        # 上传state["data"]到S3
        try:
            # 生成唯一的文件名，包含时间戳
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            trend_word = state["data"].get("trend_word", "unknown")
            s3_data_path = f"svg_video/{self.project_hash}/state_data_{timestamp}.json"
            
            # 使用临时文件
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
                # 将state["data"]直接写入临时文件
                json.dump(state["data"], temp_file, ensure_ascii=False, indent=2)
                temp_file_path = temp_file.name
            
            # 上传临时文件到S3
            print(f"正在上传状态数据到S3: {s3_data_path}")
            self.s3_handler.put_object(s3_data_path, temp_file_path)
            
            # 获取S3文件URL
            s3_url = self.s3_handler.get_file_url(s3_data_path)
            print(f"状态数据已上传到S3，URL: {s3_url}")
            
            # 记录上传路径到state
            state["data"]["s3_state_data_path"] = s3_data_path
            state["data"]["s3_state_data_url"] = s3_url
            
            # 删除临时文件
            import os
            os.unlink(temp_file_path)
            
        except Exception as e:
            print(f"上传状态数据到S3失败: {e}")
            
        # 重新保存包含S3路径的状态数据到本地
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已更新状态数据，包含S3路径")