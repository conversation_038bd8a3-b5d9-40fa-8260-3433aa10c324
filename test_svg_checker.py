import os
import json
from typing import Dict, Any, List, Tuple
from unittest.mock import patch, MagicMock

from nodes.svg_code_checker import SVGCodeChecker
from nodes.director_editor_worker import <PERSON><PERSON><PERSON>orWorker

def create_mock_state() -> Dict[str, Any]:
    """创建模拟状态数据，匹配实际的数据结构"""
    # 创建一个模拟的状态数据，基于实际截图中的数据结构
    return {
        "data": {
            "trend_word": "曹杨细胞再生医院",
            "project_dir": "test_project",
            "data_state_path": "s3://test-bucket/test-project/state_data.json",
            "scene_paths": [
                ("section_1_shot_1", "s3://test-bucket/test-project/section_1_shot_1.mp4"),
                ("section_2_shot_1", "s3://test-bucket/test-project/section_2_shot_1.mp4")
            ],
            "news_report": {
                "title": "曹杨细胞再生与萌宠医院新闻报道",
                "topic_type_determined": "B. 学校社区类",
                "sections": [
                    {
                        "title": "事件概述",
                        "content": "概述内容"
                    },
                    {
                        "title": "学校与萌宠: 天价药环萌件",
                        "type": "body",
                        "subsections": [
                            {
                                "title": "萌犬与再生药物故事",
                                "content": "2023年5月11日，\"萌宠\"萌柯细胞在学校成为热门话题...",
                                "key_points": ["4 个元素..."],
                                "assets": {
                                    "characterAssets": [
                                        {
                                            "name": "曹杨细胞",
                                            "description": "萌柯的拟人化角色，卡通风，因西药认知凭借药物引发对科学医疗和爱心的宣扬",
                                            "aspectRatio": "3:4",
                                            "image_type": "jpg",
                                            "path": "曹杨细胞.jpg",
                                            "s3_url": "http://intra-d.sinastorage.com/svg_video/e2fc7be75c36b68ffb5b1ce2cfdcdb9_claude/曹杨细胞.jpg"
                                        }
                                    ],
                                    "propAssets": [
                                        {
                                            "name": "曹杨细胞GRAFF耳环",
                                            "description": "曹杨细胞成为热门的抢购珠宝（GRAFF）相关钻石耳环。公价高达2300万元，是当季爆款",
                                            "aspectRatio": "1:1",
                                            "image_type": "jpg",
                                            "path": "曹杨细胞凯撒graff耳环.jpg",
                                            "s3_url": "http://intra-d.sinastorage.com/svg_video/e2fc7be75c36b68ffb5b1ce2cfdcdb9_claude/曹杨细胞凯撒graff耳环.jpg"
                                        }
                                    ]
                                },
                                "section_script": "# 萌犬与药环萌宠故事 - 第一部分：萌柯信息 (Asset Information)...",
                                "conversation_log": ["3 个元素..."],
                                "svg_prompt": "# 动画设计要求...",
                                "svg_code": "<svg width=\"2560\" height=\"1800\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">...</svg>",
                                "svg_path": "svg_video/e2fc7be75c36b68ffb5b1ce2cfdcdb9/scene_section_2_shot_1.svg",
                                "svg_code_review_conversation_log": ["3 个元素..."]
                            }
                        ]
                    }
                ]
            }
        },
        "current_step": "previous_step"
    }

def test_save_to_database():
    """测试_save_to_database方法"""
    print("开始测试SVGCodeChecker的_save_to_database方法")
    
    # 创建模拟对象
    mock_db_pool = MagicMock()
    mock_write_project_info = MagicMock(return_value=123)  # 返回模拟的项目ID
    mock_write_project_scene = MagicMock(return_value=456)  # 返回模拟的场景ID
    mock_write_scene_material = MagicMock(return_value=789)  # 返回模拟的材料ID
    
    # 模拟SVGUtils
    mock_svg_utils = MagicMock()
    
    # 使用patch替换数据库和文件操作相关的方法
    with patch('nodes.svg_code_checker.DBConnectionPool', return_value=mock_db_pool), \
         patch('nodes.svg_code_checker.write_project_video_info', mock_write_project_info), \
         patch('nodes.svg_code_checker.write_project_video_scene', mock_write_project_scene), \
         patch('nodes.svg_code_checker.write_project_video_scene_material', mock_write_scene_material), \
         patch('nodes.svg_code_checker.SVGUtils', return_value=mock_svg_utils), \
         patch('nodes.svg_code_checker.FileManager'):
        
        # 创建SVGCodeChecker实例
        checker = SVGCodeChecker()
        
        # 创建模拟状态
        state = create_mock_state()
        
        # 提取参数
        project_dir = state["data"]["project_dir"]
        data_state_path = state["data"]["data_state_path"]
        scene_paths = state["data"]["scene_paths"]
        
        try:
            # 调用_save_to_database方法
            checker._save_to_database(state, project_dir, data_state_path, scene_paths)
            
            # 验证模拟方法是否被调用
            mock_write_project_info.assert_called_once()
            
            # 验证write_project_video_scene是否被调用了正确的次数
            assert mock_write_project_scene.call_count == len(scene_paths), \
                f"write_project_video_scene应被调用{len(scene_paths)}次，实际调用了{mock_write_project_scene.call_count}次"
            
            # 验证write_project_video_scene_material是否被调用
            # 预期的调用次数：第二个场景的第一个分镜有1个角色和1个道具，总共2次
            expected_material_calls = 2
            assert mock_write_scene_material.call_count == expected_material_calls, \
                f"write_project_video_scene_material应被调用{expected_material_calls}次，实际调用了{mock_write_scene_material.call_count}次"
            
            # 验证调用write_project_video_scene_material时使用的参数
            # 检查第一次调用（角色资产）
            args1, _ = mock_write_scene_material.call_args_list[0]
            assert args1[4] == "曹杨细胞.jpg" or args1[4].endswith("曹杨细胞.jpg"), \
                f"第一次调用应使用路径'曹杨细胞.jpg'，实际使用了'{args1[4]}'"
            
            # 检查第二次调用（道具资产）
            args2, _ = mock_write_scene_material.call_args_list[1]
            assert args2[4] == "曹杨细胞凯撒graff耳环.jpg" or args2[4].endswith("曹杨细胞凯撒graff耳环.jpg"), \
                f"第二次调用应使用路径'曹杨细胞凯撒graff耳环.jpg'，实际使用了'{args2[4]}'"
            
            print("测试完成，所有断言通过")
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            print(traceback.format_exc())

def test_update_assets_from_xml_images():
    """测试_update_assets_from_xml_images方法"""
    print("\n开始测试DirectorEditorWorker的_update_assets_from_xml_images方法")
    
    # 创建DirectorEditorWorker实例
    worker = DirectorEditorWorker()
    
    # 准备测试数据
    subsection = {
        "title": "测试子章节",
        "content": "测试内容",
        "key_points": ["测试关键点"],
        "assets": {
            "characterAssets": [],
            "propAssets": []
        }
    }
    
    # 准备XML图片数据
    xml_images = [
        {
            "name": "测试角色",
            "type": "character",
            "description": "这是一个测试角色",
            "aspect_ratio": "3:4",
            "image_type": "jpg",
            "path": "test_character.jpg"
        },
        {
            "name": "测试道具",
            "type": "prop",
            "description": "这是一个测试道具",
            "aspect_ratio": "1:1",
            "image_type": "jpg",
            "path": "test_prop.jpg"
        }
    ]
    
    try:
        # 调用_update_assets_from_xml_images方法
        worker._update_assets_from_xml_images(subsection, xml_images)
        
        # 验证角色资产是否被正确添加
        assert len(subsection["assets"]["characterAssets"]) == 1, \
            f"应有1个角色资产，实际有{len(subsection['assets']['characterAssets'])}个"
        
        # 验证道具资产是否被正确添加
        assert len(subsection["assets"]["propAssets"]) == 1, \
            f"应有1个道具资产，实际有{len(subsection['assets']['propAssets'])}个"
        
        # 验证角色资产的内容
        character_asset = subsection["assets"]["characterAssets"][0]
        assert character_asset["name"] == "测试角色", \
            f"角色资产名称应为'测试角色'，实际为'{character_asset['name']}'"
        assert character_asset["description"] == "这是一个测试角色", \
            f"角色资产描述应为'这是一个测试角色'，实际为'{character_asset['description']}'"
        assert character_asset["aspectRatio"] == "3:4", \
            f"角色资产宽高比应为'3:4'，实际为'{character_asset['aspectRatio']}'"
        
        # 验证道具资产的内容
        prop_asset = subsection["assets"]["propAssets"][0]
        assert prop_asset["name"] == "测试道具", \
            f"道具资产名称应为'测试道具'，实际为'{prop_asset['name']}'"
        assert prop_asset["description"] == "这是一个测试道具", \
            f"道具资产描述应为'这是一个测试道具'，实际为'{prop_asset['description']}'"
        assert prop_asset["aspectRatio"] == "1:1", \
            f"道具资产宽高比应为'1:1'，实际为'{prop_asset['aspectRatio']}'"
        
        # 测试重复添加相同名称的资产
        worker._update_assets_from_xml_images(subsection, xml_images)
        
        # 验证资产没有重复添加
        assert len(subsection["assets"]["characterAssets"]) == 1, \
            f"重复添加后应仍有1个角色资产，实际有{len(subsection['assets']['characterAssets'])}个"
        assert len(subsection["assets"]["propAssets"]) == 1, \
            f"重复添加后应仍有1个道具资产，实际有{len(subsection['assets']['propAssets'])}个"
        
        print("测试完成，所有断言通过")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_save_to_database()
    test_update_assets_from_xml_images() 