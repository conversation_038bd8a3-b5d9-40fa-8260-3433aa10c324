from typing import List, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from llm.base import LLMProvider

class ConversationManager:
    """对话管理器"""

    def __init__(self, provider: LLMProvider):
        """
        初始化对话管理器

        Args:
            provider: LLM提供者
        """
        self.provider = provider
        self.history: List[BaseMessage] = []
        self.system_prompt: Optional[str] = None

    def set_system_prompt(self, system_prompt: str):
        """
        设置系统提示词

        Args:
            system_prompt: 系统提示词
        """
        self.system_prompt = system_prompt

    def add_user_message(self, content: str):
        """
        添加用户消息

        Args:
            content: 消息内容
        """
        self.history.append(HumanMessage(content=content))

    def add_assistant_message(self, content: str):
        """
        添加助手消息

        Args:
            content: 消息内容
        """
        self.history.append(AIMessage(content=content))

    def clear_history(self):
        """清空历史记录"""
        self.history = []

    async def get_response(self, query: str, enable_thinking: bool = False, thinking_budget_tokens: int = 2000) -> str:
        """
        获取响应

        Args:
            query: 用户查询
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算

        Returns:
            str: 生成的响应文本
        """
        if not self.system_prompt:
            raise ValueError("系统提示词未设置")

        # 创建消息列表
        messages = self.provider.create_messages(
            system_prompt=self.system_prompt,
            history=self.history,
            query=query
        )

        # 设置最大重试次数
        max_retries = 5
        retry_count = 0
        response = ""

        while retry_count < max_retries:
            try:
                # 生成响应
                response = await self.provider.generate_response(
                    messages=messages,
                    enable_thinking=enable_thinking,
                    thinking_budget_tokens=thinking_budget_tokens
                )
                print(f"生成响应，第{retry_count + 1}次尝试")

                # 检查响应是否为fail
                if response != "fail":
                    print(f"成功生成响应，第{retry_count + 1}次尝试")
                    break

                print(f"生成响应失败，第{retry_count + 1}次尝试")
                retry_count += 1

            except Exception as e:
                print(f"生成过程出错: {e}")
                retry_count += 1
                print(f"正在进行第{retry_count + 1}次尝试...")

            # 如果重试失败，等待一秒再试
            if retry_count < max_retries:
                import time
                time.sleep(1)

        # 如果所有重试都失败，使用默认响应
        if retry_count >= max_retries and (response == "fail"):
            print(f"经过{max_retries}次尝试，仍然无法获取有效的响应")
            response = "无法生成有效的响应。请检查输入数据或模型配置。"

        # 更新历史记录
        self.add_user_message(query)
        self.add_assistant_message(response)

        return response
