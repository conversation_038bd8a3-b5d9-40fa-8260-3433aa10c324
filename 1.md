# 动画设计文档\n\n## 1. 整体概念与目标\n* **动画总时长：** 14.0秒\n* **动画核心内容/叙事：** 展示2025年黄杨钿甜成人礼上佩戴的高价耳环如何引发争议，按时间顺序呈现从分享照片、耳环被关注、价格争议、本人回应到引发对家庭财富质疑的完整事件脉络。\n* **动画风格与目的：** 信息图表式+现代简约风格，以严肃专业的调性客观呈现新闻事件，通过动态元素展示事件发展过程。\n\n## 2. 画布尺寸与输出格式\n* **画布尺寸：** 2560×1080像素 (宽高比 21:9，高清格式)\n* **最终输出格式：** SVG动画 (内联SMIL或CSS动画，引用外部图片素材)\n\n## 3. 主要组成部分\n\n### 3.1 人物元素列表\n* **人物名称/代号：** 黄杨钿甜\n    * **描述：** 事件的核心人物，童星，在成人礼上佩戴高价耳环引发争议\n    * **资产路径：** data/assets/characters/yhdt.jpg\n    * **原始尺寸：** [聊天记录未提及，建议保持原始比例]\n    * **动画中显示尺寸：** 开场时约720×960像素(3:4比例)，之后根据场景需求变化\n    * **初始位置（中心点坐标）：** (1280, 540)\n    * **图层顺序：** 10\n    * **关键动画行为：** 以淡入效果出现并轻微放大(105%-100%)，随后向左移动并缩小，最终移至画面左上角\n\n### 3.2 道具/图形元素列表\n* **道具/图形名称/代号：** GRAFF耳环\n    * **描述：** 黄杨钿甜成人礼上佩戴的格拉夫祖母绿钻石耳环，价值争议约230万元\n    * **资产路径：** data/assets/props/yhdt_earring.jpg\n    * **原始尺寸：** [聊天记录未提及，但指定为1:1比例]\n    * **动画中显示尺寸：** 初始约400×400像素，放大后约756×756像素\n    * **初始位置（中心点坐标）：** (1800, 540)\n    * **图层顺序：** 15\n    * **关键动画行为：** 通过放大镜效果从人物照片中\"提取\"，随后放大占据主要画面，最终全屏展示并添加模糊效果\n\n* **道具/图形名称/代号：** 放大镜效果\n    * **描述：** 用于突出展示耳环细节的视觉工具\n    * **资产路径：** [程序生成图形]\n    * **动画中显示尺寸：** 直径约500像素的圆形\n    * **初始位置（中心点坐标）：** (1500, 540)\n    * **图层顺序：** 20\n    * **关键动画行为：** 从人物照片中耳环位置出现，移动到右侧并放大，突出耳环细节\n\n* **道具/图形名称/代号：** 价格标签\n    * **描述：** 展示耳环价格争议的动态图形元素\n    * **资产路径：** [程序生成图形]\n    * **动画中显示尺寸：** 约300×150像素\n    * **初始位置（中心点坐标）：** (1800, 700)\n    * **图层顺序：** 25\n    * **关键动画行为：** 出现在耳环旁边，显示从\"15万\"到\"230万\"的价格变化\n\n* **道具/图形名称/代号：** 虚线连接线\n    * **描述：** 连接人物照片中耳环位置和放大后耳环图片的虚线\n    * **资产路径：** [程序生成图形]\n    * **动画中显示尺寸：** 长度随元素位置变化\n    * **初始位置：** 起点于人物照片中耳环位置，终点于放大耳环\n    * **图层顺序：** 18\n    * **关键动画行为：** 随放大镜效果动态绘制，连接原位置和特写\n\n### 3.3 背景设计\n* **背景类型：** 纯色背景\n* **颜色/资产路径：** #FFFFFF (白色背景)\n* **动画效果：** 后期添加轻微模糊+暗色调覆盖效果，时间范围12-14秒\n\n### 3.4 文字关键词元素\n* **文字内容：** \"2025年5月11日\"\n    * **出现时间范围：** 0.5s - 3.0s\n    * **位置：** (1280, 100)\n    * **字体：** [聊天记录未提及，建议使用无衬线体如Arial/Helvetica]\n    * **字号：** 36px\n    * **颜色：** #333333\n    * **对齐方式：** 居中\n    * **动画效果：** 从上方滑入，0.5秒完成\n    * **图层顺序：** 30\n\n* **文字内容：** \"黄杨钿甜成人礼\"\n    * **出现时间范围：** 1.0s - 3.0s\n    * **位置：** (1280, 980)\n    * **字体：** 白色简约字体，带淡蓝色描边\n    * **字号：** 48px\n    * **颜色：** #FFFFFF 带#B0E0E6描边\n    * **对齐方式：** 居中\n    * **动画效果：** 从底部向上滑入，0.5秒完成\n    * **图层顺序：** 30\n\n* **文字内容：** \"GRAFF祖母绿钻石耳环\"\n    * **出现时间范围：** 4.0s - 6.0s\n    * **位置：** (1800, 350)\n    * **字体：** 优雅衬线字体\n    * **字号：** 42px\n    * **颜色：** 浅金色 (#FFD700)\n    * **对齐方式：** 居中\n    * **动画效果：** 从右侧滑入，0.5秒完成\n    * **图层顺序：** 30\n\n* **文字内容：** \"价值争议：15万-230万?\"\n    * **出现时间范围：** 7.0s - 9.0s\n    * **位置：** (1800, 850)\n    * **字体：** 醒目字体\n    * **字号：** 44px\n    * **颜色：** 红色 (#FF0000)\n    * **对齐方式：** 居中\n    * **动画效果：** 从下方弹出，配合价格标签动画\n    * **图层顺序：** 30\n\n* **文字内容：** \"向母亲借用\"\n    * **出现时间范围：** 9.5s - 12.0s\n    * **位置：** (1700, 540)\n    * **字体：** [聊天记录未提及，建议使用无衬线体]\n    * **字号：** 40px\n    * **颜色：** 蓝色渐变 (#0066CC)\n    * **对齐方式：** 居中\n    * **动画效果：** 以引号样式文字框呈现，从右侧滑入，带有缓慢跳动动画\n    * **图层顺序：** 35\n\n* **文字内容：** \"引发财富来源质疑\"\n    * **出现时间范围：** 12.5s - 14.0s\n    * **位置：** (1280, 800)\n    * **字体：** [聊天记录未提及，建议使用无衬线体]\n    * **字号：** 46px\n    * **颜色：** 从蓝色(#0066CC)过渡到深紫色(#4B0082)的渐变\n    * **对齐方式：** 居中\n    * **动画效果：** 从下方向上弹出\n    * **图层顺序：** 35\n\n* **文字内容：** \"导火索：成人礼上的争议耳环\"\n    * **出现时间范围：** 12.0s - 14.0s\n    * **位置：** (1280, 540)\n    * **字体：** 标题字体\n    * **字号：** 52px\n    * **颜色：** #FFFFFF 带深色描边\n    * **对齐方式：** 居中\n    * **动画效果：** 以强调动画在画面中央显现\n    * **图层顺序：** 40\n\n* **文字内容：** 多个争议点文字气泡 (\"财富来源?\"、\"家庭背景?\"、\"调查\")\n    * **出现时间范围：** 12.5s - 14.0s\n    * **位置：** 围绕耳环图片周围多个位置\n    * **字体：** [聊天记录未提及，建议使用无衬线体]\n    * **字号：** 32px\n    * **颜色：** 深紫色 (#4B0082)\n    * **对齐方式：** 居中\n    * **动画效果：** 逐个淡入并轻微放大\n    * **图层顺序：** 38\n\n## 4. 时间线结构\n\n### 4.1 元素动画轨道列表 (Element Animation Tracks)\n\n* **轨道ID/元素名称：** 黄杨钿甜照片-开场\n    * **作用元素：** 黄杨钿甜照片\n    * **动画时间范围：** 0.0s - 3.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：0.0\n        * 缩放比例 (x, y)：(1.05, 1.05)\n        * 旋转角度：0\n        * 其他属性：无\n    * **动画效果描述：** 照片以淡入效果出现，同时从105%缩小至100%大小，营造聚焦感\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n        * 旋转角度：0\n    * **缓动函数：** ease-out\n    * **关键帧：**\n        * 0.0s: 透明度=0, 缩放=(1.05, 1.05)\n        * 0.5s: 透明度=1, 缩放=(1.03, 1.03)\n        * 3.0s: 透明度=1, 缩放=(1.0, 1.0)\n\n* **轨道ID/元素名称：** 黄杨钿甜照片-移位\n    * **作用元素：** 黄杨钿甜照片\n    * **动画时间范围：** 3.0s - 6.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **动画效果描述：** 照片从中央位置移动到左侧，同时缩小至原来的40%\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(640, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(0.4, 0.4)\n    * **缓动函数：** ease-in-out\n    * **关键帧：**\n        * 3.0s: 位置=(1280, 540), 缩放=(1.0, 1.0)\n        * 4.0s: 位置=(640, 540), 缩放=(0.4, 0.4)\n\n* **轨道ID/元素名称：** 黄杨钿甜照片-最终缩小\n    * **作用元素：** 黄杨钿甜照片\n    * **动画时间范围：** 6.0s - 9.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(640, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(0.4, 0.4)\n    * **动画效果描述：** 照片进一步缩小并移至画面左上角\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(400, 270)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(0.2, 0.2)\n    * **缓动函数：** ease-in-out\n\n* **轨道ID/元素名称：** 放大镜效果\n    * **作用元素：** 放大镜图形\n    * **动画时间范围：** 3.7s - 5.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(700, 480)\n        * 透明度：0\n        * 缩放比例 (x, y)：(0.5, 0.5)\n    * **动画效果描述：** 放大镜从黄杨钿甜耳环位置出现，移动到右侧并放大\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1500, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **缓动函数：** ease-out\n    * **关键帧：**\n        * 3.7s: 位置=(700, 480), 透明度=0, 缩放=(0.5, 0.5)\n        * 4.2s: 位置=(1200, 510), 透明度=1, 缩放=(0.8, 0.8)\n        * 5.0s: 位置=(1500, 540), 透明度=1, 缩放=(1.0, 1.0)\n\n* **轨道ID/元素名称：** 耳环图片-出现\n    * **作用元素：** GRAFF耳环图片\n    * **动画时间范围：** 4.0s - 6.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1500, 540)\n        * 透明度：0\n        * 缩放比例 (x, y)：(0, 0)\n    * **动画效果描述：** 耳环图片从放大镜位置淡入并放大\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1800, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **缓动函数：** elastic\n    * **关键帧：**\n        * 4.0s: 位置=(1500, 540), 透明度=0, 缩放=(0, 0)\n        * 4.5s: 位置=(1650, 540), 透明度=0.5, 缩放=(0.6, 0.6)\n        * 5.0s: 位置=(1800, 540), 透明度=1, 缩放=(1.0, 1.0)\n\n* **轨道ID/元素名称：** 虚线连接\n    * **作用元素：** 虚线连接线\n    * **动画时间范围：** 4.0s - 5.5s\n    * **初始状态/属性：**\n        * 位置：起点(700, 480)，终点(700, 480)\n        * 透明度：0\n        * 长度：0\n    * **动画效果描述：** 从人物照片中耳环位置到放大后的耳环图片间绘制一条虚线\n    * **最终状态/属性：**\n        * 位置：起点(700, 480)，终点(1600, 540)\n        * 透明度：1.0\n        * 完整长度\n    * **缓动函数：** linear\n    * **关键帧：**\n        * 4.0s: 透明度=0, 长度=0\n        * 4.3s: 透明度=1, 长度=30%\n        * 5.5s: 透明度=1, 长度=100%\n\n* **轨道ID/元素名称：** 耳环图片-放大\n    * **作用元素：** GRAFF耳环图片\n    * **动画时间范围：** 6.0s - 9.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1800, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **动画效果描述：** 耳环图片放大并移至画面中央\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1400, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.5, 1.5)\n    * **缓动函数：** ease-in-out\n\n* **轨道ID/元素名称：** 价格标签\n    * **作用元素：** 价格标签图形\n    * **动画时间范围：** 7.0s - 9.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1800, 700)\n        * 透明度：0\n        * 缩放比例 (x, y)：(0.8, 0.8)\n        * 内部文字：\"15万\"\n    * **动画效果描述：** 价格标签出现，数字从\"15万\"快速跳动到\"230万\"\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1800, 700)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n        * 内部文字：\"230万\"\n    * **缓动函数：** elastic\n    * **关键帧：**\n        * 7.0s: 透明度=0, 内部文字=\"15万\"\n        * 7.3s: 透明度=1, 内部文字=\"15万\"\n        * 7.5s: 内部文字=\"50万\"\n        * 7.7s: 内部文字=\"120万\"\n        * 7.9s: 内部文字=\"180万\"\n        * 8.1s: 内部文字=\"230万\"，缩放=(1.1, 1.1)\n        * 8.3s: 缩放=(1.0, 1.0)\n\n* **轨道ID/元素名称：** 耳环图片-全屏\n    * **作用元素：** GRAFF耳环图片\n    * **动画时间范围：** 9.0s - 12.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1400, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.5, 1.5)\n    * **动画效果描述：** 耳环图片继续放大至全屏\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.9, 1.9)\n    * **缓动函数：** ease-in\n\n* **轨道ID/元素名称：** 引号样式文字框\n    * **作用元素：** \"向母亲借用\"文字框\n    * **动画时间范围：** 9.5s - 12.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(2000, 540)\n        * 透明度：0\n        * 缩放比例 (x, y)：(0.8, 0.8)\n    * **动画效果描述：** 引号样式文字框从右侧滑入，带有轻微跳动动画\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1700, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **缓动函数：** ease-out\n    * **关键帧：**\n        * 9.5s: 位置=(2000, 540), 透明度=0\n        * 10.0s: 位置=(1700, 540), 透明度=1\n        * 10.5s: 缩放=(1.05, 1.05)\n        * 11.0s: 缩放=(1.0, 1.0)\n        * 11.5s: 缩放=(1.05, 1.05)\n        * 12.0s: 缩放=(1.0, 1.0)\n\n* **轨道ID/元素名称：** 背景模糊效果\n    * **作用元素：** 背景和耳环图片\n    * **动画时间范围：** 12.0s - 14.0s\n    * **初始状态/属性：** 模糊半径: 0px，暗色调覆盖: 0%\n    * **动画效果描述：** 背景和耳环图片逐渐应用模糊效果和暗色调覆盖，象征争议和质疑\n    * **最终状态/属性：** 模糊半径: 3px，暗色调覆盖: 30%\n    * **缓动函数：** ease-in\n    * **关键帧：**\n        * 12.0s: 模糊半径=0px，暗色调覆盖=0%\n        * 13.0s: 模糊半径=1.5px，暗色调覆盖=15%\n        * 14.0s: 模糊半径=3px，暗色调覆盖=30%\n\n* **轨道ID/元素名称：** 争议点文字气泡\n    * **作用元素：** 多个文字气泡\n    * **动画时间范围：** 12.5s - 14.0s\n    * **初始状态/属性：**\n        * 位置：围绕耳环图片周围多个位置\n        * 透明度：0\n        * 缩放比例：(0.7, 0.7)\n    * **动画效果描述：** 多个文字气泡(\"财富来源?\"、\"家庭背景?\"、\"调查\"等)逐个淡入并轻微放大\n    * **最终状态/属性：**\n        * 位置：围绕耳环图片周围多个位置\n        * 透明度：1.0\n        * 缩放比例：(1.0, 1.0)\n    * **缓动函数：** ease-out\n    * **关键帧：**\n        * 12.5s: 气泡1出现，透明度=0\n        * 12.7s: 气泡1透明度=1，气泡2出现，透明度=0\n        * 12.9s: 气泡2透明度=1，气泡3出现，透明度=0\n        * 13.1s: 气泡3透明度=1\n\n* **轨道ID/元素名称：** 标题强调\n    * **作用元素：** \"导火索：成人礼上的争议耳环\"文字\n    * **动画时间范围：** 12.0s - 14.0s\n    * **初始状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：0\n        * 缩放比例 (x, y)：(0.5, 0.5)\n    * **动画效果描述：** 标题文字在中央以强调动画显现\n    * **最终状态/属性：**\n        * 位置 (中心点x, y)：(1280, 540)\n        * 透明度：1.0\n        * 缩放比例 (x, y)：(1.0, 1.0)\n    * **缓动函数：** elastic\n    * **关键帧：**\n        * 12.0s: 透明度=0, 缩放=(0.5, 0.5)\n        * 12.5s: 透明度=0.5, 缩放=(1.2, 1.2)\n        * 13.0s: 透明度=1, 缩放=(1.0, 1.0)\n\n### 4.2 视觉时间线概览 (ASCII Representation)\n```\n时间(秒)  0.0   1.0   2.0   3.0   4.0   5.0   6.0   7.0   8.0   9.0   10.0  11.0  12.0  13.0  14.0\n          |-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|-----|\n黄杨-开场  |==============================|\n黄杨-移位                |==============================|\n黄杨-缩小                                |==============================|\n日期标题    |=========================|\n成人礼标题      |=====================|\n放大镜效果                  |=================|\n耳环图片-出现                   |=================|\n虚线连接                     |=================|\n耳环名称                        |=============|\n耳环-放大                               |==============================|\n价格标签                                  |==========|\n价格争议文字                                |==========|\n耳环-全屏                                            |==============================|\n引号样式框                                              |========================|\n向母亲借用                                              |========================|\n背景模糊                                                                  |==========|\n争议点气泡                                                               |==========|\n质疑文字                                                                 |==========|\n标题强调                                                                 |==========|\n```\n\n### 4.3 分镜/场景与音频同步列表\n* **时间戳：** `0.0s ~ 3.0s`\n    * **音频/独白内容：** \"2025年5月11日，黄杨钿甜分享成人礼照片，\"\n    * **主要视觉描述：** \"黄杨钿甜照片居中展示，以淡入效果出现并轻微放大至正常大小。画面上方显示日期'2025年5月11日'，下方显示'黄杨钿甜成人礼'标题。\"\n    * **涉及元素：** 黄杨钿甜照片、日期文字、成人礼标题\n\n* **时间戳：** `3.0s ~ 6.0s`\n    * **音频/独白内容：** \"佩戴的GRAFF祖母绿钻石耳环引发网友关注。\"\n    * **主要视觉描述：** \"黄杨钿甜照片移至左侧并缩小至40%，右侧出现放大镜效果从照片中'提取'耳环细节，并通过虚线连接原位置。耳环图片以动画方式出现在右侧，上方显示'GRAFF祖母绿钻石耳环'文字。\"\n    * **涉及元素：** 黄杨钿甜照片、放大镜效果、虚线连接、耳环图片、耳环名称文字\n\n* **时间戳：** `6.0s ~ 9.0s`\n    * **音频/独白内容：** \"这对耳环被传价值高达230万元，\"\n    * **主要视觉描述：** \"黄杨钿甜照片进一步缩小并移至左上角，耳环图片放大并移至画面中央。下方出现价格标签，数字从'15万'快速跳动到'230万'，同时显示'价值争议：15万-230万?'文字。\"\n    * **涉及元素：** 黄杨钿甜照片、耳环图片、价格标签、价格争议文字\n\n* **时间戳：** `9.0s ~ 12.0s`\n    * **音频/独白内容：** \"黄随后回应称耳环是向母亲借用，\"\n    * **主要视觉描述：** \"耳环图片继续放大至全屏展示，右侧出现引号样式文字框，显示'向母亲借用'，带有轻微跳动动画效果。\"\n    * **涉及元素：** 耳环图片、引号样式文字框、\"向母亲借用\"文字\n\n* **时间戳：** `12.0s ~ 14.0s`\n    * **音频/独白内容：** \"却意外引发了对其家庭财富来源的一系列调查与质疑。\"\n    * **主要视觉描述：** \"耳环图片保持全屏，但逐渐应用轻微模糊和暗色调覆盖效果。周围浮现多个文字气泡，包含关键争议点。画面中央出现'导火索：成人礼上的争议耳环'标题，下方显示'引发财富来源质疑'文字。\"\n    * **涉及元素：** 耳环图片、背景模糊效果、争议点文字气泡、标题文字、质疑文字\n\n## 5. 重要说明与约束\n* **素材引用：** 所有图片素材严格使用 `3.1` 和 `3.2` 中提取的 `资产路径`。SVG文件中应通过 `<image xlink:href=\"资产路径\" ... />` 方式引用。\n* **坐标系：** 画布左上角为 (0,0)，X轴向右为正，Y轴向下为正。\n* **尺寸与比例：** 所有元素在动画过程中除非明确指示缩放，否则应保持其在 `3.1` 和 `3.2` 中定义的 `动画中显示尺寸` 和原始宽高比。\n* **元素不重叠：** 除非设计意图或聊天记录明确指示，否则应避免动画元素在视觉上不期望地重叠。若有重叠，确保图层顺序（Z-index）符合预期。\n* **动画平滑度：** 动画应尽可能平滑，帧率感觉应在24fps以上（虽然SVG动画不直接指定帧率，但效果应流畅）。\n* **SVG实现方式：** 动画优先考虑使用SVG SMIL (Synchronized Multimedia Integration Language) 实现。对于SMIL难以表达的复杂交互或动效，可考虑内联CSS动画。避免外部JavaScript依赖，除非聊天记录特别指明。\n* **文字元素透明度限制：** 文字元素永远不要使用透明度动画，应使用其他方式（如位移、缩放、颜色变化等）实现文字的出现和消失效果。",
            "svg_code": "<svg width=\"2560\" height=\"1080\" viewBox=\"0 0 2560 1080\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n  <!-- 定义滤镜 -->\n  <defs>\n    <filter id=\"blur-effect\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feGaussianBlur in=\"SourceGraphic\" stdDeviation=\"0\">\n        <animate attributeName=\"stdDeviation\" from=\"0\" to=\"3\" begin=\"12s\" dur=\"2s\" fill=\"freeze\" />\n      </feGaussianBlur>\n    </filter>\n    \n    <filter id=\"magnify-effect\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n      <feGaussianBlur stdDeviation=\"1\" />\n    </filter>\n    \n    <linearGradient id=\"blueGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n      <stop offset=\"0%\" style=\"stop-color:#0066CC\" />\n      <stop offset=\"100%\" style=\"stop-color:#0066CC\" />\n    </linearGradient>\n    \n    <linearGradient id=\"purpleGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n      <stop offset=\"0%\" style=\"stop-color:#0066CC\" />\n      <stop offset=\"100%\" style=\"stop-color:#4B0082\" />\n      <animate attributeName=\"x1\" from=\"0%\" to=\"100%\" begin=\"12.5s\" dur=\"1.5s\" fill=\"freeze\" />\n    </linearGradient>\n  </defs>\n  \n  <!-- 背景 -->\n  <rect id=\"background\" x=\"0\" y=\"0\" width=\"2560\" height=\"1080\" fill=\"#FFFFFF\" />\n  \n  <!-- 暗色调覆盖 -->\n  <rect id=\"overlay\" x=\"0\" y=\"0\" width=\"2560\" height=\"1080\" fill=\"#000000\" opacity=\"0\">\n    <animate attributeName=\"opacity\" from=\"0\" to=\"0.3\" begin=\"12s\" dur=\"2s\" fill=\"freeze\" />\n  </rect>\n  \n  <!-- 黄杨钿甜照片 -->\n  <g id=\"yhdt-photo\">\n    <image id=\"yhdt-img\" xlink:href=\"data/assets/characters/yhdt.jpg\" x=\"920\" y=\"60\" width=\"720\" height=\"960\" opacity=\"0\">\n      <!-- 淡入效果 -->\n      <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"0s\" dur=\"0.5s\" fill=\"freeze\" />\n      \n      <!-- 初始缩放 -->\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.05\" to=\"1.03\" \n                        begin=\"0s\" dur=\"0.5s\" additive=\"sum\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.03\" to=\"1\" \n                        begin=\"0.5s\" dur=\"2.5s\" additive=\"sum\" fill=\"freeze\" />\n      \n      <!-- 移位动画 -->\n      <animateTransform attributeName=\"transform\" type=\"translate\" from=\"0 0\" to=\"-640 0\" \n                        begin=\"3s\" dur=\"1s\" additive=\"sum\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1\" to=\"0.4\" \n                        begin=\"3s\" dur=\"1s\" additive=\"sum\" fill=\"freeze\" />\n      \n      <!-- 最终缩小 -->\n      <animateTransform attributeName=\"transform\" type=\"translate\" from=\"-640 0\" to=\"-880 -270\" \n                        begin=\"6s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.4\" to=\"0.2\" \n                        begin=\"6s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n    </image>\n  </g>\n  \n  <!-- 放大镜效果 -->\n  <g id=\"magnifier\" opacity=\"0\">\n    <circle cx=\"700\" cy=\"480\" r=\"250\" fill=\"none\" stroke=\"#333333\" stroke-width=\"5\" stroke-dasharray=\"10,5\">\n      <animate attributeName=\"cx\" from=\"700\" to=\"1500\" begin=\"3.7s\" dur=\"1.3s\" fill=\"freeze\" />\n      <animate attributeName=\"cy\" from=\"480\" to=\"540\" begin=\"3.7s\" dur=\"1.3s\" fill=\"freeze\" />\n    </circle>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"3.7s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animate attributeName=\"opacity\" from=\"1\" to=\"0\" begin=\"5s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 虚线连接 -->\n  <g id=\"connection-line\" opacity=\"0\">\n    <line x1=\"700\" y1=\"480\" x2=\"700\" y2=\"480\" stroke=\"#333333\" stroke-width=\"3\" stroke-dasharray=\"10,5\">\n      <animate attributeName=\"x2\" from=\"700\" to=\"1600\" begin=\"4s\" dur=\"1.5s\" fill=\"freeze\" />\n      <animate attributeName=\"y2\" from=\"480\" to=\"540\" begin=\"4s\" dur=\"1.5s\" fill=\"freeze\" />\n    </line>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"4s\" dur=\"0.3s\" fill=\"freeze\" />\n    <animate attributeName=\"opacity\" from=\"1\" to=\"0\" begin=\"5.5s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- GRAFF耳环 -->\n  <g id=\"earring-group\">\n    <image id=\"earring-img\" xlink:href=\"data/assets/props/yhdt_earring.jpg\" x=\"1600\" y=\"340\" width=\"400\" height=\"400\" opacity=\"0\">\n      <!-- 出现动画 -->\n      <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"4s\" dur=\"1s\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"translate\" from=\"-100 0\" to=\"200 0\" \n                        begin=\"4s\" dur=\"1s\" additive=\"sum\" fill=\"freeze\" calcMode=\"spline\"\n                        keySplines=\"0.42 0 0.58 1\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0\" to=\"1\" \n                        begin=\"4s\" dur=\"1s\" additive=\"sum\" fill=\"freeze\" calcMode=\"spline\"\n                        keySplines=\"0.42 0 0.58 1\" />\n      \n      <!-- 放大动画 -->\n      <animateTransform attributeName=\"transform\" type=\"translate\" from=\"200 0\" to=\"-200 0\" \n                        begin=\"6s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1\" to=\"1.5\" \n                        begin=\"6s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n      \n      <!-- 全屏动画 -->\n      <animateTransform attributeName=\"transform\" type=\"translate\" from=\"-200 0\" to=\"-320 0\" \n                        begin=\"9s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n      <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.5\" to=\"1.9\" \n                        begin=\"9s\" dur=\"3s\" additive=\"sum\" fill=\"freeze\" />\n      \n      <!-- 模糊效果 -->\n      <animate attributeName=\"filter\" from=\"none\" to=\"url(#blur-effect)\" begin=\"12s\" dur=\"0.1s\" fill=\"freeze\" />\n    </image>\n  </g>\n  \n  <!-- 价格标签 -->\n  <g id=\"price-tag\" opacity=\"0\" transform=\"translate(1800, 700)\">\n    <rect x=\"-150\" y=\"-75\" width=\"300\" height=\"150\" rx=\"20\" ry=\"20\" fill=\"#FFFFCC\" stroke=\"#FFD700\" stroke-width=\"3\" />\n    <text id=\"price-text\" x=\"0\" y=\"15\" font-family=\"Arial, sans-serif\" font-size=\"48px\" font-weight=\"bold\" fill=\"#FF0000\" text-anchor=\"middle\">15万</text>\n    \n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"7s\" dur=\"0.3s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.8\" to=\"1\" additive=\"sum\"\n                      begin=\"7s\" dur=\"0.3s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1\" to=\"1.1\" additive=\"sum\"\n                      begin=\"8.1s\" dur=\"0.1s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.1\" to=\"1\" additive=\"sum\"\n                      begin=\"8.2s\" dur=\"0.1s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 价格文本动画 -->\n  <set xlink:href=\"#price-text\" attributeName=\"textContent\" to=\"15万\" begin=\"7s\" />\n  <set xlink:href=\"#price-text\" attributeName=\"textContent\" to=\"50万\" begin=\"7.5s\" />\n  <set xlink:href=\"#price-text\" attributeName=\"textContent\" to=\"120万\" begin=\"7.7s\" />\n  <set xlink:href=\"#price-text\" attributeName=\"textContent\" to=\"180万\" begin=\"7.9s\" />\n  <set xlink:href=\"#price-text\" attributeName=\"textContent\" to=\"230万\" begin=\"8.1s\" />\n  \n  <!-- 引号样式文字框 -->\n  <g id=\"quote-box\" opacity=\"0\" transform=\"translate(2000, 540)\">\n    <rect x=\"-150\" y=\"-50\" width=\"300\" height=\"100\" rx=\"15\" ry=\"15\" fill=\"#E6F7FF\" stroke=\"#0066CC\" stroke-width=\"3\" />\n    <text x=\"0\" y=\"15\" font-family=\"Arial, sans-serif\" font-size=\"40px\" fill=\"url(#blueGradient)\" text-anchor=\"middle\">向母亲借用</text>\n    <path d=\"M-130,-30 Q-140,-40 -150,-30 L-150,0 Q-140,-10 -130,0 Z\" fill=\"#0066CC\" />\n    \n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"9.5s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"translate\" from=\"2000 540\" to=\"1700 540\" \n                      begin=\"9.5s\" dur=\"0.5s\" fill=\"freeze\" />\n    \n    <!-- 跳动动画 -->\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1\" to=\"1.05\" additive=\"sum\"\n                      begin=\"10.5s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.05\" to=\"1\" additive=\"sum\"\n                      begin=\"11s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1\" to=\"1.05\" additive=\"sum\"\n                      begin=\"11.5s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 争议点文字气泡 -->\n  <g id=\"bubble-1\" opacity=\"0\" transform=\"translate(1100, 400)\">\n    <ellipse cx=\"0\" cy=\"0\" rx=\"100\" ry=\"60\" fill=\"#F0E6FF\" stroke=\"#4B0082\" stroke-width=\"2\" />\n    <text x=\"0\" y=\"10\" font-family=\"Arial, sans-serif\" font-size=\"32px\" fill=\"#4B0082\" text-anchor=\"middle\">财富来源?</text>\n    \n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"12.5s\" dur=\"0.2s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.7\" to=\"1\" additive=\"sum\"\n                      begin=\"12.5s\" dur=\"0.3s\" fill=\"freeze\" />\n  </g>\n  \n  <g id=\"bubble-2\" opacity=\"0\" transform=\"translate(1450, 350)\">\n    <ellipse cx=\"0\" cy=\"0\" rx=\"100\" ry=\"60\" fill=\"#F0E6FF\" stroke=\"#4B0082\" stroke-width=\"2\" />\n    <text x=\"0\" y=\"10\" font-family=\"Arial, sans-serif\" font-size=\"32px\" fill=\"#4B0082\" text-anchor=\"middle\">家庭背景?</text>\n    \n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"12.7s\" dur=\"0.2s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.7\" to=\"1\" additive=\"sum\"\n                      begin=\"12.7s\" dur=\"0.3s\" fill=\"freeze\" />\n  </g>\n  \n  <g id=\"bubble-3\" opacity=\"0\" transform=\"translate(1300, 700)\">\n    <ellipse cx=\"0\" cy=\"0\" rx=\"80\" ry=\"50\" fill=\"#F0E6FF\" stroke=\"#4B0082\" stroke-width=\"2\" />\n    <text x=\"0\" y=\"10\" font-family=\"Arial, sans-serif\" font-size=\"32px\" fill=\"#4B0082\" text-anchor=\"middle\">调查</text>\n    \n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"12.9s\" dur=\"0.2s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.7\" to=\"1\" additive=\"sum\"\n                      begin=\"12.9s\" dur=\"0.3s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 文字元素 -->\n  <!-- 日期文字 -->\n  <g id=\"date-text\">\n    <text x=\"1280\" y=\"0\" font-family=\"Arial, sans-serif\" font-size=\"36px\" fill=\"#333333\" text-anchor=\"middle\">\n      2025年5月11日\n      <animate attributeName=\"y\" from=\"0\" to=\"100\" begin=\"0.5s\" dur=\"0.5s\" fill=\"freeze\" />\n    </text>\n  </g>\n  \n  <!-- 成人礼标题 -->\n  <g id=\"ceremony-title\">\n    <text x=\"1280\" y=\"1100\" font-family=\"Arial, sans-serif\" font-size=\"48px\" fill=\"#FFFFFF\" stroke=\"#B0E0E6\" stroke-width=\"1\" text-anchor=\"middle\">\n      黄杨钿甜成人礼\n      <animate attributeName=\"y\" from=\"1100\" to=\"980\" begin=\"1s\" dur=\"0.5s\" fill=\"freeze\" />\n    </text>\n  </g>\n  \n  <!-- 耳环名称 -->\n  <g id=\"earring-name\" opacity=\"0\">\n    <text x=\"1800\" y=\"350\" font-family=\"Georgia, serif\" font-size=\"42px\" fill=\"#FFD700\" text-anchor=\"middle\">\n      GRAFF祖母绿钻石耳环\n    </text>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"4s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animate attributeName=\"opacity\" from=\"1\" to=\"0\" begin=\"6s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 价格争议文字 -->\n  <g id=\"price-dispute\" opacity=\"0\">\n    <text x=\"1800\" y=\"850\" font-family=\"Impact, sans-serif\" font-size=\"44px\" fill=\"#FF0000\" text-anchor=\"middle\">\n      价值争议：15万-230万?\n    </text>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"7s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animate attributeName=\"opacity\" from=\"1\" to=\"0\" begin=\"9s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 财富来源质疑 -->\n  <g id=\"wealth-question\" opacity=\"0\">\n    <text x=\"1280\" y=\"900\" font-family=\"Arial, sans-serif\" font-size=\"46px\" fill=\"url(#purpleGradient)\" text-anchor=\"middle\">\n      引发财富来源质疑\n      <animate attributeName=\"y\" from=\"900\" to=\"800\" begin=\"12.5s\" dur=\"0.5s\" fill=\"freeze\" />\n    </text>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"12.5s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n  \n  <!-- 标题强调 -->\n  <g id=\"main-title\" opacity=\"0\" transform=\"translate(1280, 540) scale(0.5)\">\n    <text x=\"0\" y=\"0\" font-family=\"Arial Black, sans-serif\" font-size=\"52px\" fill=\"#FFFFFF\" stroke=\"#333333\" stroke-width=\"2\" text-anchor=\"middle\">\n      导火索：成人礼上的争议耳环\n    </text>\n    <animate attributeName=\"opacity\" from=\"0\" to=\"1\" begin=\"12s\" dur=\"0.5s\" fill=\"freeze\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"0.5\" to=\"1.2\" additive=\"sum\"\n                      begin=\"12s\" dur=\"0.5s\" fill=\"freeze\" calcMode=\"spline\"\n                      keySplines=\"0.42 0 0.58 1\" />\n    <animateTransform attributeName=\"transform\" type=\"scale\" from=\"1.2\" to=\"1\" additive=\"sum\"\n                      begin=\"12.5s\" dur=\"0.5s\" fill=\"freeze\" />\n  </g>\n</svg>