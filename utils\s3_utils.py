# -*- coding:utf-8 -*-
import os
import sys
import configparser
import json

work_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

sys.path.append(f'{work_dir}/')
sys.path.append(f'{work_dir}/S3_SDK_Python_3x/')

import SinaStorage_3x
from SinaStorage_3x.bucket import SCSBucket, ACL, FAST_ACL


class SVGSaveS3:
    """SVG文件S3存储工具类"""
    
    def __init__(self, conf_path):
        """
        初始化S3存储工具类
        
        Args:
            conf_path: 配置文件路径
        """
        self.config_path = conf_path
        self._load_config()
        self.init_S3()

    def init_S3(self):
        """初始化S3连接"""
        SinaStorage_3x.setDefaultAppInfo(self.access_key, self.secret_key)
        self.s_handler = SCSBucket(self.bucket_name, secure=False)

    def get_buckets_meta(self):
        """获取存储桶元数据"""
        ret = self.s_handler.get_bucket_meta()
        return ret

    def put_object(self, s3_path, source_path):
        """
        上传文件到S3
        
        Args:
            s3_path: S3上的路径
            source_path: 本地文件路径
            
        Returns:
            上传结果
        """
        res = self.s_handler.put_object(s3_path, source_path)
        return res

    def put_object_by_content(self, s3_path, content, mimetype="image/svg+xml"):
        """
        通过内容上传文件到S3
        
        Args:
            s3_path: S3上的路径
            content: 文件内容
            mimetype: 文件类型
            
        Returns:
            上传结果
        """
        res = self.s_handler.put_object_by_content(s3_path, content, mimetype=mimetype)
        return res

    def get_object(self, s3_path, source_path):
        """
        从S3下载文件
        
        Args:
            s3_path: S3上的路径
            source_path: 本地保存路径
            
        Returns:
            下载结果
        """
        res = self.s_handler.get_object(s3_path, source_path)
        return res

    def delete_object(self, key):
        """
        删除S3上的文件
        
        Args:
            key: 文件路径
            
        Returns:
            删除结果
        """
        res = self.s_handler.delete_object(key)
        return res

    def list_object(self, prefix='', marker='', limit=10, delimiter=''):
        """
        列出S3上的文件
        
        Args:
            prefix: 前缀
            marker: 标记
            limit: 限制数量
            delimiter: 分隔符
            
        Returns:
            文件列表
        """
        files_generator = self.s_handler.list_object(prefix=prefix, marker=marker, delimiter=delimiter)
        return files_generator

    def list_files(self, prefix=''):
        """
        获取指定前缀下的文件列表并返回格式化结果
        
        Args:
            prefix: 目录前缀
            
        Returns:
            文件路径列表
        """
        try:
            # 调用原始的list_object方法
            self.s_handler.list_object(prefix=prefix)
            
            # 由于原始SDK输出到控制台而不是返回值，这里通过URL构建来获取文件列表
            # 注意：这是一个变通方法，不是理想的实现
            # 真实环境中应查看SDK源码或与SDK提供者联系
            
            # 构建测试URL并返回
            test_url = self.get_file_url(f"{prefix.rstrip('/')}/" if prefix else "")
            return {
                "bucket": self.bucket_name,
                "domain": self.s3_domain,
                "prefix": prefix,
                "test_url": test_url,
                "note": "SDK的list_object方法直接输出到控制台，没有返回结果。这是一个变通的结果格式。"
            }
        except Exception as e:
            return {"error": str(e)}

    def get_file_url(self, s3_path):
        """
        获取文件的URL
        
        Args:
            s3_path: S3上的路径
            
        Returns:
            文件URL
        """
        return f"http://{self.s3_domain}/{s3_path}"

    def _load_config(self):
        """
        从ini配置文件加载配置
        """
        try:
            config = configparser.ConfigParser()
            ret = config.read(self.config_path, encoding='utf-8')
            # 读取API配置段
            section = config['SINA_S3']
            self.bucket_name = section.get("bucket")
            self.access_key = section.get("access_key")
            self.secret_key = section.get("secret_key")
            self.s3_domain = section.get("s3_domain")
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试代码
    s3 = SVGSaveS3(r"D:\code\generate-svg\conf\conf.ini")
    
    try:
        # 测试获取存储桶元数据

        print("获取存储桶元数据:")
        meta = s3.get_buckets_meta()
        print(f"存储桶元数据: {meta}")
        
        # 使用新的list_files方法
        print("\n使用list_files方法列出文件:")
        prefix = "svg_video/e2fc7be75c360b68ffb5b1ce2cfdcdb9/"
        file_info = s3.list_files(prefix=prefix)
        print(json.dumps(file_info, indent=2))
        
        # 尝试获取一个特定文件的URL
        print("\n构建文件URL:")
        test_file_url = s3.get_file_url(f"{prefix}file1.svg")
        print(f"测试文件URL: {test_file_url}")
        
        # 打印更多的bucket信息
        print("\nBucket信息:")
        print(f"Bucket名称: {s3.get_object}")
        print(f"S3域名: {s3.s3_domain}")
    except Exception as e:
        print(f"执行过程中出错: {str(e)}")
