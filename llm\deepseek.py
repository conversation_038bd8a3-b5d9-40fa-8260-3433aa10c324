import os
from typing import List
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain.chat_models import ChatDeepSeek
from llm.base import LLMProvider

class DeepSeekProvider:
    """DeepSeek模型提供者"""

    def __init__(self):
        """初始化DeepSeek模型"""
        self.model = ChatDeepSeek(
            model="deepseek-chat",
            temperature=0,
            api_key=os.getenv("DEEPSEEK_API_KEY")
        )

    async def generate_response(self, messages: List[BaseMessage], enable_thinking: bool = False, thinking_budget_tokens: int = 2000) -> str:
        """
        生成响应

        Args:
            messages: 消息列表
            enable_thinking: 是否启用思考模式
            thinking_budget_tokens: 思考模式的token预算

        Returns:
            str: 生成的响应文本
        """
        # 注意：DeepSeek可能不支持thinking模式，这里忽略thinking相关参数
        response = await self.model.ainvoke(messages)
        return response.content

    def create_messages(self,
                       system_prompt: str,
                       history: List[BaseMessage],
                       query: str) -> List[BaseMessage]:
        """
        创建消息列表

        Args:
            system_prompt: 系统提示词
            history: 历史消息
            query: 用户查询

        Returns:
            List[BaseMessage]: 消息列表
        """
        messages = [SystemMessage(content=system_prompt)]
        messages.extend(history)
        messages.append(HumanMessage(content=query))
        return messages