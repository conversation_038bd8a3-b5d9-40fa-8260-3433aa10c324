import re
from typing import Tuple
from lxml import etree
import io

class SVGUtils:
    """SVG工具类，提供SVG解析和验证功能"""

    @staticmethod
    def extract_svg_code(text: str) -> str:
        """
        从文本中提取SVG代码

        Args:
            text: 包含SVG代码的文本

        Returns:
            str: 提取的SVG代码
        """
        # 尝试查找<svg>和</svg>之间的内容
        svg_pattern = r'<svg[\s\S]*?</svg>'
        matches = re.findall(svg_pattern, text)

        if matches:
            # 返回第一个匹配的SVG内容
            return matches[0]

        # 尝试查找```xml或```svg和```之间的内容
        code_pattern = r'```(?:xml|svg)\s*([\s\S]*?)\s*```'
        matches = re.findall(code_pattern, text)

        if matches:
            # 检查提取的内容是否包含<svg>标签
            for match in matches:
                if '<svg' in match and '</svg>' in match:
                    return match.strip()

        # 如果没有找到完整的SVG代码，返回空字符串
        return ""

    @staticmethod
    def validate_svg(svg_code: str) -> Tuple[bool, str]:
        """
        验证SVG代码的有效性

        Args:
            svg_code: SVG代码字符串

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 创建XML解析器，禁用外部实体以提高安全性
            parser = etree.XMLParser(resolve_entities=False, no_network=True)
            
            # 添加xlink命名空间定义
            if 'xlink:href' in svg_code and 'xmlns:xlink' not in svg_code:
                svg_code = svg_code.replace('<svg', '<svg xmlns:xlink="http://www.w3.org/1999/xlink"')
            
            # 尝试解析SVG代码
            etree.parse(io.StringIO(svg_code), parser)
            return True, ""
        except etree.XMLSyntaxError as e:
            # 返回具体的错误信息
            return False, f"SVG语法错误: {str(e)}"
        except Exception as e:
            return False, f"SVG验证错误: {str(e)}"
