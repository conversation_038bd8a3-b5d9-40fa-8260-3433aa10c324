import os
from typing import Dict

class PromptManager:
    """提示词管理器"""
    
    def __init__(self, prompt_dir: str = "prompt"):
        """
        初始化提示词管理器
        
        Args:
            prompt_dir: 提示词目录
        """
        self.prompt_dir = prompt_dir
        self.prompts: Dict[str, str] = {}
        self._load_prompts()
    
    def _load_prompts(self):
        """加载所有提示词文件"""
        for filename in os.listdir(self.prompt_dir):
            if filename.endswith(".md"):
                prompt_name = filename.split(".")[0]
                with open(os.path.join(self.prompt_dir, filename), "r", encoding="utf-8") as f:
                    self.prompts[prompt_name] = f.read()
    
    def get_prompt(self, prompt_name: str) -> str:
        """
        获取指定提示词
        
        Args:
            prompt_name: 提示词名称
            
        Returns:
            str: 提示词内容
        """
        return self.prompts.get(prompt_name, "")
    
    def format_prompt(self, prompt_name: str, **kwargs) -> str:
        """
        格式化提示词
        
        Args:
            prompt_name: 提示词名称
            **kwargs: 格式化参数
            
        Returns:
            str: 格式化后的提示词
        """
        prompt = self.get_prompt(prompt_name)
        return prompt.format(**kwargs) 