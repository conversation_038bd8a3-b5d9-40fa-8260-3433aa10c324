import asyncio
import sys
import os
import time
import json
from datetime import datetime

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm.claude_local import ClaudeLocalProvider
from langchain_core.messages import SystemMessage, HumanMessage
from utils.prompt_utils import PromptManager

async def test_svg_deep_thinking():
    """测试SVG生成的深度思考功能"""
    print("=" * 50)
    print("SVG生成深度思考功能测试")
    print("=" * 50)
    
    # 创建Claude本地提供者
    claude_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-3-7-sonnet",
        max_tokens=36000
    )
    
    # 加载提示词
    prompt_manager = PromptManager()
    system_prompt = "你是一个AI提示词专家，擅长生成 svg 动画代码"
    
    # 准备测试问题 - 这是一个需要深度思考的SVG生成任务
    svg_prompt = """
    # SVG动画设计文档生成提示词

    你是一位专业的SVG动画设计师和技术文档撰写专家。请根据用户提供的输入内容，创建一个详细的SVG动画设计文档，遵循以下格式和要求。

    ## 输入
    1. **text**：事故发生后，贵州省立即启动应急预案，调集248名消防员，其中包括83名专业潜水员赶赴现场。救援人员面临的是极为复杂的情况：船体倒扣在水面，部分未正确穿戴救生衣的游客被困船舱；湍急的江水不断将落水者冲向下游；天色渐暗，增加了搜寻难度。'我们到达现场时，能见度极低，水下搜救几乎只能靠触摸，'一位参与救援的潜水员事后回忆。令人感动的是，附近村民闻讯后立即划船参与救援，在专业力量到达前挽救了多名落水者。救援队伍沿乌江下游50公里设立了多个拦截点，同时调用无人机、热成像设备和救援直升机进行全方位搜寻。值得一提的是，一名女船长在船只侧翻前的果断操作，成功将自己负责的船只驶向避风区域，保护了全船乘客安全，被誉为事故中的英雄。
    2. **props**：{'characterAssets': [{'name': '专业潜水员', 'description': ' 身着专业潜水装备的救援人员，正在进行水下搜救任务。他们在低能见度的水下环境中主要靠触摸寻找被困人员。', 'aspectRatio': '3:4', 'image_type': 'jpg', 'path': 'data/asset/characters/专业潜水员.jpg'}, {'name': '消防救援人员', 'description': '身着橙色或红色救援服的消防员，在岸边或船上进行救援协调和支援工作，装备齐全，表情严肃专注。', 'aspectRatio': '3:4', 'image_type': 'jpg', 'path': 'data/asset/characters/消防救援人员.jpg'}, {'name': '女船长', 'description': '临危不乱的女船长，身着船员制服，表情坚定，正在驾驶船只避开危险区域，被誉为事故中的英雄。', 'aspectRatio': '3:4', 'image_type': 'jpg', 'path': 'data/asset/characters/女船长.jpg'}, {'name': '当地村民救援者', 'description': '身着普通衣物的当地村民，划着小船参与救援工作，展现了紧急情况下的民间力量和团结精神。', 'aspectRatio': '3:4', 'image_type': 'jpg', 'path': 'data/asset/characters/当地村民救援者.jpg'}, {'name': '被困游客', 'description': '在船舱内被困或在水中挣扎的游客，有些未正确穿戴救生衣，表情惊恐，等待救援。', 'aspectRatio': '3:4', 'image_type': 'jpg', 'path': 'data/asset/characters/被困游客.jpg'}], 'propAssets': [{'name': '倒扣船只', 'description': '在乌江水面上倒扣的游船，显示事故现场的严峻情况，部分船体露出水面。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/倒扣船只.jpg'}, {'name': '救援直升机', 'description': '在事故水域上空盘旋的救援直升机，配备搜救设备，参与空中救援和侦查工作。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/救援直升机.jpg'}, {'name': '救援无人机', 'description': '配备热成像设备的救援无人机，在夜间低空飞行搜寻落水人员。', 'aspectRatio': '4:3', 'image_type': 'jpg', 'path': 'data/asset/props/救援无人机.jpg'}, {'name': '村民救援小船', 'description': '当地村民用于参与救援的传统小木船或小渔船，简单但在紧急情况下发挥重要作用。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/村民救援小船.jpg'}, {'name': '热成像设备', 'description': '救援人员手持的热成像设备，用于在能见度 低的环境中搜寻生命体征。', 'aspectRatio': '1:1', 'image_type': 'jpg', 'path': 'data/asset/props/热成像设备.jpg'}, {'name': '救生衣', 'description': '橙色或红色的标准救生衣，事故中一些游客未正确穿戴这一关键安全装备。', 'aspectRatio': '3:4', 'image_type': 'png', 'path': 'data/asset/props/救生衣.png'}, {'name': '乌江湍急水域', 'description': '湍急的乌江水域，波浪汹涌，体现救援环境的危险和困难。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/乌江湍急水域.jpg'}, {'name': '救援拦截点', 'description': '沿乌江下游设立的救援拦截点，包括岸边的帐篷、设备和待命的救援人员。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/救援拦截点.jpg'}, {'name': '潜水装备', 'description': '专业潜水员使用的潜水装备，包括氧气罐、潜水面罩、防水手电筒等，用于水下搜救行动。', 'aspectRatio': '3:4', 'image_type': 'png', 'path': 'data/asset/props/潜水装备.png'}, {'name': '安全避风区域', 'description': '乌江上相对平静的避风区域，是女船长 成功驶入保护乘客的安全地带。', 'aspectRatio': '16:9', 'image_type': 'jpg', 'path': 'data/asset/props/安全避风区域.jpg'}]}

    ## 输出要求

    请生成一个完整的SVG动画设计文档，文件名为`generate_svg.md`，包含以下所有部分：

    ### 1. 整体概念
    - 基于输入的text创建一个5秒钟动画的概念描述
    - 保持 简洁明了，突出核心信息

    ### 2. 画布尺寸与格式
    - 尺寸：1920×1080像素（16:9高清格式）
    - 帧率：60fps
    - 输出格式：SVG动画

    ### 3. 主要组成部分
    将所 有视觉元素分为5个类别：

    #### 3.1 人物元素
    - 严格使用输入中提供的人物资产
    - 保留完整的描述、位置和尺寸信息
    - 格式必须包含：描述、位置（asset路径） 、尺寸（根据宽高比计算的合适宽度和高度）

    #### 3.2 道具元素
    - 严格使用输入中提供的道具资产
    - 保留完整的描述、位置和尺寸信息
    - 格式必须包含：描述、 位置（asset路径）、尺寸（根据宽高比计算的合适宽度和高度）

    #### 3.3 背景设计
    - 创建适合动画概念的背景元素
    - 背景默认为白色
    - 只需简单描述，无需详细的位置和尺寸信息

    #### 3.4 文字关键词元素
    - 从输入的text中提取关键词
    - 只包含关键词，不包含完整字幕
    - 只需简单描述，无需详细的位置和尺寸信息

    #### 3.5 装饰元素
    - 创建增强视觉效果的装饰元素
    - 只需简单描述，无需详细的位置和尺寸信息

    ### 4. 时间线结构（5秒动画）

    #### 4.1 轨道布局与分类
    - 将 所有元素按5个类别组织（背景、道具、人物、文字关键词、装饰元素）
    - **每个元素必须占据独立的轨道**，特别是每个人物元素和每个道具元素必须各自占据一条独立 轨道
    - 为每个轨道提供：
      - 元素名称
      - 中心点坐标（精确到像素）
      - 持续时间范围（精确到0.1秒）
      - 特殊动画效果（如适用）
      - 元素在轨道上的变 化（位置、大小、透明度等）

    #### 4.2 视觉时间线表示
    - 创建ASCII艺术图表，显示5秒时间线上各元素的出现和消失
    - 格式参考：
    ```
    时间(秒)  0.0   1.0   2.0   3.0   4.0   5.0
              |     |     |     |     |     |
    基础背景   |=====================================|
    元素1     |===========|
        |========|
    元素2               |===============|
    ...
    ```

    #### 4.3 分场景时间线
    - 将5秒动画分为2-4个场景
    - 为每个场景提供时间范围和关键元素描述

    ### 5. 布局分析

    #### 5.1 空间布局概览
    - 画布尺寸：1920×1080像素
    - 中心点：(960, 540)
    - 列出所有主要元素的位置坐标

    #### 5.2 资产素材详细信息
    - 为所有道具元素和人物元素提供详细信息：
      - 位置（asset路径）
      - 中心点坐标（精确到像素，例如(960, 540)）
      - 尺寸（根据宽高比计算的合适宽度和 高度像素值）
      - 特性描述
      - 颜色信息（如适用）
      - 初始位置和最终位置（如果元素在动画中移动）
      - **确保所有素材之间不重叠**，合理安排每个元素的位置和大小

    #### 5.3 简化布局图示
    - 创建ASCII艺术图表，显示主要元素在画布上的位置分布
    - 确保图表清晰展示各元素的相对位置，验证元素之间没有重叠

    ### 6. 视觉效果与技术实现

    #### 6.1 动画特效
    - 色彩处理：为主要元素定义颜色方案
    - SVG动画技术：列出使用的动画技术（不透明度、旋转、缩放、颜色过渡等）
    - 特殊效果：描述任何特殊视觉效果

    #### 6.2 技术实现要点
    - SVG动画实现方式
    - 响应式设计考虑
    - 性能优化建议
    - 素材引用实现：
      - 对于所有引用的素材 （JPG/PNG或SVG动画）：统一使用`<image>`元素，例如：
        ```xml
        <image xlink:href="asset/props/example.jpg" x="起始X坐标" y="起始Y坐标" width="计算宽度" height="计算高度"/>
        <image xlink:href="asset/characters/example.svg" x="起始X坐标" y="起始Y坐标" width="计算宽度" height="计算高度"/>
        ```
      - 根据素材的宽高比和画布布局，计算合适的宽度和高度，确保元素之间不重叠
      - 高度必须根据aspectRatio和计算的宽度自动计算

    ### 7. 重要说明

    #### 7.1 素材使用规范
    - 道具元素和人物元素必须严格使用项目asset目录下的对应素材
    - 其他元素（背景、文字、装饰）可根据描述自行生成
    - 素材引用必须遵循以下规 则：
      - 所有素材必须通过其path属性引用，例如`asset/characters/trump_figure.svg`
      - 所有素材必须保持原有比例，只调整整体大小
      - 根据素材的宽高比和 画布布局，计算合适的宽度和高度
      - 高度必须根据dimensions.aspectRatio和计算的宽度自动计算
      - 所有引用的素材（JPG/PNG或SVG动画）必须统一使用`<image>` 元素引用
      - 确保所有素材之间不重叠，合理安排每个元素的位置和大小

    #### 7.2 动画规范
    - 强调严格遵循5秒的时间线结构
    - 确保元素在指定时间点出现和消失
    - 保持动画流畅性和视觉连贯性
    - 确保文字关键词元素只包含关键词，不包含完整字幕

    ## 特别注意事项

    1. 人物元素和道具元素必须使用项目asset目录下的素 材，禁止自行生成
    2. 每个人物和道具元素必须在时间线布局中单独作为一轨
    3. 素材布局需包含每个素材资产的详细信息（中心点坐标、宽高比、计算的宽度和高度、位置信息）
    4. 除了对话气泡外，画面中不应包含字幕和任何描述性的句子，只能出现关键词
    5. 所有元素的动画时间必须严格控制在5秒内
    6. 所有坐标必须精确到像素，时间必须精确到0.1秒
    7. 生成的SVG代码中必须严格按照提供的资产素材元素的path引用素材：
       - 所有引用的素材（JPG/PNG或SVG动画）：统一使用`<image xlink:href="资产路径" width="计算宽度" height="计算高度"/>`元素
    8. 根据素材的宽高比和画布布局，计算合适的宽度和高度，确保元素之间不重叠
    9. 高度必须根据dimensions.aspectRatio和计算的宽度自动计算
    """
    
    # 创建消息
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=svg_prompt)
    ]
    
    # 测试启用深度思考的情况
    print("\n[测试] 启用深度思考模式生成SVG设计文档:")
    start_time = time.time()
    response_with_thinking = await claude_provider.generate_response(
        messages=messages,
        enable_thinking=True,
        thinking_budget_tokens=2000
    )
    thinking_time = time.time() - start_time
    print(f"响应时间: {thinking_time:.2f}秒")
    print(f"响应长度: {len(response_with_thinking)} 字符")
    
    # 保存启用深度思考的响应
    with open("svg_design_with_thinking.md", "w", encoding="utf-8") as f:
        f.write(response_with_thinking)
    print("已将启用深度思考的响应保存到 svg_design_with_thinking.md")
    
    # 提取SVG代码
    import re
    svg_pattern = r'<svg[\s\S]*?</svg>'
    matches = re.findall(svg_pattern, response_with_thinking)
    
    if matches:
        # 保存SVG代码
        with open("svg_output_with_thinking.svg", "w", encoding="utf-8") as f:
            f.write(matches[0])
        print("已将SVG代码保存到 svg_output_with_thinking.svg")
    else:
        print("未在响应中找到SVG代码")

async def main():
    """主函数"""
    # 测试SVG深度思考功能
    await test_svg_deep_thinking()
    
    print("\n所有测试完成!")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
