import aiohttp
import asyncio
import os
from typing import Optional, Tuple, Dict, Any
import logging


class SinaImageUtils:
    """新浪微博图片下载工具，处理防盗链问题"""
    
    def __init__(self):
        """初始化新浪图片下载工具"""
        # 设置默认请求头，模拟浏览器绕过防盗链
        self.default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://weibo.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    async def download_image(self, url: str, save_path: str, 
                          custom_headers: Optional[Dict[str, str]] = None,
                          timeout: int = 30) -> Tuple[bool, str]:
        """
        下载新浪微博图片（处理防盗链）
        
        Args:
            url: 新浪微博图片链接
            save_path: 保存图片的路径
            custom_headers: 自定义请求头
            timeout: 请求超时时间（秒）
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        headers = self.default_headers.copy()
        if custom_headers:
            headers.update(custom_headers)
            
        # 确保保存目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        
        try:
            async with aiohttp.ClientSession() as session:
                # 设置超时
                timeout_obj = aiohttp.ClientTimeout(total=timeout)
                
                # 发起请求
                async with session.get(url, headers=headers, timeout=timeout_obj) as response:
                    if response.status != 200:
                        error_msg = f"下载失败: HTTP状态码 {response.status}"
                        self.logger.error(error_msg)
                        return False, error_msg
                    
                    # 读取响应内容并保存图片
                    data = await response.read()
                    
                    # 检查数据是否为有效图片（简单检查文件头）
                    if not self._is_valid_image(data):
                        error_msg = "下载的数据不是有效的图片"
                        self.logger.error(error_msg)
                        return False, error_msg
                    
                    # 保存图片
                    with open(save_path, 'wb') as f:
                        f.write(data)
                    
                    success_msg = f"图片成功下载到: {save_path}"
                    self.logger.info(success_msg)
                    return True, success_msg
                    
        except aiohttp.ClientError as e:
            error_msg = f"请求错误: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
        except asyncio.TimeoutError:
            error_msg = f"请求超时: {timeout}秒"
            self.logger.error(error_msg)
            return False, error_msg
        except IOError as e:
            error_msg = f"文件操作错误: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _is_valid_image(self, data: bytes) -> bool:
        """
        简单检查数据是否为有效图片
        
        Args:
            data: 图片二进制数据
            
        Returns:
            bool: 是否为有效图片
        """
        if not data:
            return False
            
        # 检查常见图片格式的文件头
        jpg_header = b'\xff\xd8\xff'
        png_header = b'\x89PNG\r\n\x1a\n'
        gif_header = b'GIF8'
        webp_header = b'RIFF'
        
        return (data.startswith(jpg_header) or 
                data.startswith(png_header) or 
                data.startswith(gif_header) or 
                data.startswith(webp_header))
    
    @staticmethod
    def convert_image_url(url: str, size: str = "large") -> str:
        """
        转换新浪微博图片URL到指定大小
        
        Args:
            url: 原始图片URL
            size: 目标大小，可选值:
                - "large": 原图
                - "mw2000": 较大缩略图
                - "mw1024": 中等缩略图
                - "mw690": 小缩略图
                - "bmiddle": 中等缩略图
                - "thumbnail": 缩略图
                - "square": 方形缩略图
        
        Returns:
            str: 转换后的URL
        """
        # 如果URL中已经包含大小标识，则替换它
        size_patterns = ["large", "mw2000", "mw1024", "mw690", "bmiddle", "thumbnail", "square"]
        
        # 检查URL是否已包含大小标识
        for pattern in size_patterns:
            if pattern in url:
                # 替换为目标大小
                return url.replace(pattern, size)
        
        # 如果URL不包含大小标识，则返回原URL
        return url


# 使用示例
async def download_example():
    """示例：下载新浪微博图片"""
    sina_utils = SinaImageUtils()
    
    # 图片URL和保存路径
    url = "https://wx3.sinaimg.cn/mw690/005M7XdTly1i1ao0oz7vuj30j60ed0wa.jpg"
    save_path = "downloads/sina_image.jpg"
    
    # 下载图片
    success, message = await sina_utils.download_image(url, save_path)
    
    if success:
        print(f"成功: {message}")
    else:
        print(f"失败: {message}")
    
    # 转换URL到原图
    large_url = sina_utils.convert_image_url(url, "large")
    print(f"原图URL: {large_url}")


# 允许单独运行此脚本进行测试
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 运行示例
    asyncio.run(download_example())

