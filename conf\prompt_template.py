# 新的剧本生成prompt
script_prompt_new = """
        # 《热搜全景》节目JSON格式剧本生成提示词
        
        请你担任一位资深短视频内容策划和剧本创作专家，为一档名为《热搜全景》的热点解析节目创作一个极具吸引力且数据严谨的完整剧本。这是一档3-5分钟的中长视频节目，旨在以生动有趣且深入的方式系统性解析热点事件的全貌。
        
        ## 节目基本信息
        
        - **节目名称**：《热搜全景》
        - **定位**：脉络梳理中长视频，系统性理解热点全貌（3-5分钟）
        - **内容深度**：深挖"为什么"和"意味着什么"
        - **Slogan**：见微知著，洞察全貌
        - **总体风格**：深入、专业、全面，富有逻辑性和分析性，同时保持生动吸引力
        - **态度特点**：客观理性，多维思考，包容性和前瞻性思维，具有同理心和人文关怀
        
        ## 你需要完成的任务
        
        请基于以下热点事件创作一个完整的《热搜全景》节目剧本：
        
        [在此处插入你想要分析的具体热点事件，例如："近期城市共享单车管理新政引发的争议"]
        
        ## 输出格式要求
        
        请将剧本以严格的JSON格式输出，生成内容中的""都替换为[]，结构如下：
        
        ```json
        {
          "title": "《热搜全景》：[热点事件简称]",
          "duration": "3-5分钟",
          "theme": "[对本期节目主题的简要描述]",
          "key_questions": ["问题1", "问题2", "问题3"],
          "sections": [
            {
              "section_name": "开场动画与品牌展示",
              "duration": "20-30秒",
              "shots": [
                {
                  "shot_id": 1,
                  "duration": "10秒",
                  "visual": "详细描述画面内容、视觉效果、转场等",
                  "narration": "旁白文案",
                  "sound_effects": "背景音乐、音效提示（可选）",
                  "key_visual_elements": ["元素1", "元素2"],
                  "data_sources": [
                    {
                      "source_type": "[官方/学术/媒体]",
                      "source_name": "[机构名称]",
                      "url": "[链接]",
                      "accessed_date": "[访问日期]"
                    }
                  ],
                  "images": [
                    {
                      "position": "[背景/画中画]",
                      "source": "[图片链接]",
                      "caption": "[图片说明]",
                      "license": "[版权信息]",
                      "data_source": "asset_id"
                    }
                  ],
                  "charts": [
                    {
                        "chart_type": "[柱状图/饼图/...]",
                        "data_source": {
                        "source_type": "[数据源类型]",
                        "source_name": "[机构名称]",
                        "url": "[链接]"
                        }
                        "data_source": "ref_id"
                    }
                  ],
                "image_requirements": {
                    "type": "具体需要的图片类型，如'实景照片'/'图表'/'动画'等",
                    "description": "详细描述所需图片的内容、风格和要求",
                    "source_suggestions": "建议的图片来源或获取方式",
                    "alternative_options": "如无法获取理想图片，可使用的替代方案"
                  },
                },
                // 更多分镜...
              ]
            },
            // 其他4个部分...
          ],
          "visual_highlights": ["数据可视化图表1", "动态效果2", "特殊转场3"],
          "key_takeaways": ["要点1", "要点2", "要点3"],
          "data_references": [
            {
              "ref_id": "ref1",
              "source": "完整数据来源信息，如'国家统计局《2024年第一季度国民经济报告》，2024年4月15日发布'",
              "url": "可查证的官方链接（如有）",
              "used_in_shots": [3, 7, 12]
            },
            // 更多引用...
          ],
          "data_visualizations": [
            {
              "chartId": 1,
              "type": "图表类型(折线图/柱状图/饼图等)",
              "title": "图表标题",
              "data": {
                // 具体数据结构
              },
              "dataSource": "对应上方data_references中的ref_id"
            },
            // 更多图表...
          ]
          "image_assets": [
            {
              "asset_id": "img1",
              "type": "图片类型，如'照片'/'图表'/'示意图'等",
              "description": "详细描述图片内容和用途",
              "suggested_sources": ["可能的图片来源1", "可能的图片来源2"],
              "used_in_shots": [2, 5],
              "copyright_notes": "版权考虑，如'需获得授权'/'可使用CC协议图片'等" 
            },
            // 更多图片资产...
          ]
        }
        ```
        
        ## 数据真实性与引用要求
        
        节目必须建立在真实、可验证的数据基础上，请严格遵循以下要求：
        
        1. **数据真实性**：
           - 所有使用的数据、统计信息和事实必须来自真实可靠的来源
           - 不得使用虚构、估算或未经验证的数据
           - 数据应尽可能是最新的，并标明具体日期
        
        2. **来源明确性**：
           - 每个数据点都必须有明确的来源信息，包括发布机构、报告名称和发布日期
           - 权威来源优先：政府官方数据、学术研究机构、权威行业协会、知名媒体的调查报告等
           - 在分镜设计中，数据展示画面必须包含来源信息
        
        3. **引用专家观点**：
           - 专家观点必须注明专家姓名、职务/职称和所属机构
           - 确保引用内容准确无误，不断章取义
           - 平衡不同立场专家的观点，避免单一视角
        
        4. **数据呈现方式**：
           - 在JSON中的`data_sources`字段明确标注每个分镜中使用的数据来源
           - 在`data_references`部分列出所有引用的完整信息
           - 设计数据呈现画面时，确保同时呈现数据来源
           - 使用 Python Matplotlib/Tableau 生成图表，代码中嵌入来源注释。
        
        5. **事实核查**：
           - 交叉验证重要数据点，确保准确性
           - 避免使用存在争议的数据，如必须使用，应同时呈现不同来源的数据进行对比
           - 明确区分事实与观点/评论
        
        ## 图片素材要求
        
        节目需要使用高质量、相关性强的图片素材，请遵循以下规范：
        
        1. **图片类型与用途**：
           - 为每个分镜设计所需的图片类型，包括但不限于：实景照片、新闻图片、数据图表、示意图、历史影像等
           - 明确每张图片的具体用途：背景素材、事件展示、人物肖像、数据可视化等
           - 在特定场景可考虑使用动画或特效替代实景图片
        
        2. **图片详细规格**：
           - 在每个分镜的`image_requirements`字段中详细说明所需图片的内容、风格、构图等具体要求
           - 标明图片可能需要的处理方式，如：裁剪、模糊处理、文字叠加、分屏展示等
           - 提供图片尺寸和布局建议，确保视觉效果最佳
        
        3. **图片来源建议**：
           - 提供具体的图片来源渠道建议，如：官方渠道发布的图片、新闻媒体图片库、公开可用的数据图表等
           - 注明版权考虑，标识哪些图片需要获得授权，哪些可以使用开放许可的素材
           - 对于难以获取的图片，提供替代方案或次优选择
        
        4. **图片与叙事的结合**：
           - 图片选择应紧密配合叙事内容，增强而非干扰信息传递
           - 设计图片转场和组合效果，创造视觉节奏
           - 确保图片能够准确传达关键信息，避免误导或歧义
        
        5. **图片库整合**：
           - 在JSON中的`image_assets`部分集中列出所有需要的图片资产
           - 明确每个图片资产使用的分镜编号，便于制作团队整体规划
           - 提供图片获取的优先级建议，区分必要图片和可选图片
        
        ## 创作要点：提升吸引力
        
        为确保剧本具有强大的吸引力和观看黏性，请特别注意：
        
        1. **开场钩子**：设计一个震撼、意外或引发好奇的开场，在前5-10秒内抓住观众注意力
           - 可使用反直觉事实、惊人数据、引人深思的问题
           - 直击观众痛点或好奇心
        
        2. **叙事技巧**：
           - 构建清晰的叙事弧，包含矛盾、转折和思考点
           - 使用"悬念-解答"结构保持观众持续关注
           - 适当使用类比和比喻，帮助理解复杂概念
        
        3. **视觉冲击**：
           - 描述引人注目的视觉元素和转场效果
           - 设计动态图表呈现方式，使数据生动有趣
           - 设计有节奏感的画面切换
        
        4. **情感共鸣**：
           - 找到热点事件与观众日常生活的连接点
           - 展示事件对不同人群的真实影响
           - 设计能引起共鸣的真实案例或场景
        
        5. **语言风格**：
           - 使用简洁有力、富有节奏感的旁白文案
           - 适当运用修辞和对比，增强表现力
           - 避免空洞说教，保持生动具体
        
        ## 剧本结构
        
        请按照以下5个板块组织剧本内容，每个板块应包含对应的分镜设计：
        
        ### 1. 开场动画与品牌展示（约20-30秒，2-3个分镜）
        - 热搜词飞入效果与动态词云
        - 引入热点事件核心
        - 提出关键问题和分析框架
        - 开场白："见微知著，洞察全貌。欢迎来到《热搜全景》，今日我们深度解析[热点事件]..."
        
        ### 2. 事件脉络梳理（约60-80秒，6-8个分镜）
        - 按时间线或逻辑顺序呈现事件发展
        - 标记关键节点和转折点
        - 引入多方视角
        - 与历史类似案例比较
        
        ### 3. 深度分析（约60-80秒，6-8个分镜）
        - 剖析事件背后深层原因
        - 多维度解读（行业、法律、社会等）
        - 引用权威观点和专业数据
        - 探讨潜在影响
        
        ### 4. 社会意义与展望（约30-40秒，3-4个分镜）
        - 讨论事件启示
        - 分析可能的发展方向和趋势
        - 引导深层次思考
        - 保持开放性结论
        
        ### 5. 结语（约10-20秒，1-2个分镜）
        - 总结核心观点
        - 提出值得关注的问题
        - 展示品牌标识和slogan
        
        ## 注意事项
        
        1. 确保内容既专业深入又通俗易懂，平衡专业性和吸引力
        2. 分镜设计要有张有弛，节奏变化，避免单调
        3. 确保JSON格式严格正确，无语法错误：
        4. 每个分镜的旁白控制在15-25字，简洁有力
        5. 视觉描述要具体可执行，避免过于抽象
        6. 所有数据必须有明确来源，不使用未经验证的数据
        7. 图片需求要详细具体，便于后期制作团队取材
        8. 始终围绕"见微知著，洞察全貌"的核心理念展开内容
        
        请基于以上要求，创作一个格式规范、数据严谨、视觉丰富、内容专业、极具吸引力的《热搜全景》节目剧本JSON文件。
"""

full_scene_prompt_new = """
        你是一位专业的新媒体视觉设计师，擅长将文字脚本转化为精美的SVG场景设计。现在需要你根据《热搜全景》节目剧本，为每个分镜创建对应的SVG图形。
        
        《热搜全景》视觉风格特点：
        - 整体色调：以深蓝色(#1a365d)和浅蓝色(#4299e1)为主色调，辅以白色(#ffffff)和亮橙色(#ed8936)作为强调色
        - 排版风格：简洁、现代、专业，强调数据可视化的清晰度
        - 图形元素：使用几何图形、连接线、箭头等元素表达逻辑关系
        - 节目标识：左上角固定展示"热搜全景"节目Logo，右下角展示"见微知著，洞察全貌"的品牌标语
        
        请根据以下剧本内容，为每个分镜创建对应的SVG图形。剧本内容如下：
        
        [这里插入上一步生成的完整JSON剧本]
        
        对于每个分镜，请创建一个符合以下要求的SVG图形：
        
        1. 技术规范：
           - SVG尺寸：1920 x 1080像素(16:9比例)
           - 使用viewBox="0 0 1920 1080"确保正确缩放
           - 所有文本必须清晰可读，主标题字号不小于40px，正文不小于24px
           - 确保所有元素都有适当的id和class属性，便于后期动画处理
        
        2. 分镜特殊要求：
           - 开场分镜：包含动态词云效果的示意(多个热搜词从四周向中心聚集)
           - 数据图表分镜：根据剧本中的数据创建准确的可视化图表
           - 时间线分镜：使用连接的节点和箭头表示事件发展脉络
           - 结语分镜：包含品牌标识和标语的突出展示
        
        3. 每个SVG分镜需包含：
           - 背景层：主题背景色或简洁的几何图案
           - 内容层：主要视觉元素(图表、图标、插图等)
           - 文字层：标题、说明文字、数据标签等
           - 品牌层：节目Logo和品牌标语
        
        请对每个分镜单独输出SVG代码，并遵循以下格式：
        
        ```svg
        <!-- 分镜ID: [单元名称-分镜序号] -->
        <!-- 分镜描述: [简短描述该分镜内容] -->
        <!-- 对应剧本文本: [分镜对应的旁白文本] -->
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080" width="1920" height="1080">
          <!-- SVG元素代码 -->
        </svg>
        
        特别注意：
        1.确保SVG代码完整且有效，可直接用于渲染
        2.所有数据可视化必须准确反映剧本中提供的数据
        3.文字内容应精简，只保留关键信息
        4.设计时考虑10秒展示时间，不要过度复杂化
        5.保持所有分镜之间的视觉风格一致性
        
        在创建数据可视化图表时，请特别注意：
        1.折线图：
            ● 使用平滑曲线(curveBasis)而非直线连接数据点
            ● 在线条下方添加半透明渐变填充
            ● 为每个数据点添加小圆点标记
            ● 确保坐标轴清晰标注
        2.柱状图：
            ● 使用渐变色填充柱体，顶部颜色较浅
            ● 为柱体添加轻微的阴影效果
            ● 在柱体顶部显示具体数值
            ● 保持适当间距，一般为柱宽的50%
        3.饼图/环形图：
            ● 使用对比鲜明但协调的颜色
            ● 在扇区之间留有小间隙(约2度)
            ● 添加清晰的图例，包含百分比
            ● 考虑为重要扇区添加轻微的"突出"效果
        4.人物/场景示意：
            ● 使用简化的线条图标表示人物
            ● 场景使用简单几何形状构建
            ● 重要元素使用品牌强调色标记
            ● 避免过于复杂的细节
"""

full_scene_prompt_tmp = """
        你是一位顶级SVG动画分镜师，现在需要为《热搜全景》节目创建一系列专业的SVG分镜动画。请根据以下提供的剧本内容，为每个分镜创建对应的SVG动画代码。
        
        # 技术规格
        - SVG分辨率：1920×1080像素（16:9横屏）
        - 主要字体：HYXuanSong35S（正文）和HYXuanSong85S（强调文本）
        - 第一幕专用字体：HYbamanti（用于热搜词展示）
        
        # 内容要求
        - 仅生成核心内容动画，无需包含热搜词、热搜信息、logo、结尾动画和字幕
        - 第一幕需展示多个热搜词，每个热搜词旁标注#Top排名（如#Top1）
        - 在关键事件处使用时间节点标记，清晰展示事件发展脉络
        
        # 视觉风格
        - 营造沉稳专业的整体氛围，突出深度分析特性
        - 分层次呈现信息，采用多面板设计
        - 使用静态与动态结合的背景元素（数据流、脉络图、光晕）增强视觉层次
        - 采用平滑过渡动画，强调内容连贯性
        - 多元化信息展示，融合数据图表、专家观点引用框、时间线元素
        - 文字排版采用多级标题系统，主次分明；重要概念使用深色背景突出；数据采用渐变色彩增强可视性
        
        # SVG动画要求
        - 每个主题段落配备对应SVG动画
        - 动画应体现思维延展和逻辑关联
        - 使用连接线、箭头等元素展示因果关系
        - 适当使用3D透视效果增强深度感
        - 关键数据点采用脉冲动画强调
        - 使用图解方式展示复杂概念（漏斗图、树状图等）
        
        # 特殊元素设计
        - 时间线设计：水平或垂直时间轴，关键节点高亮
        - 专家观点框：引用样式，配有简约头像或机构标识
        - 数据对比区：分屏或并列式布局，便于横向比较
        - 全景视角转换：用于展示不同维度分析的视角切换动画
        
        # 数据与图片处理
        - 所有数据必须真实可靠，严禁使用虚假数据
        - 所有数据必须注明来源（格式：数据来源：[机构名称]，[发布时间]）
        - 数据时间标注必须准确无误
        - 图表（如折线图、柱状图）只能使用真实数据绘制，非必要不使用折线图
        - 使用 Python Matplotlib/Tableau 生成图表，代码中嵌入来源注释。
        - 使用提供的图片时，必须使用"pic_path"变量作为image href的值
        - 根据图片描述和尺寸，将图片插入到恰当位置，并标注图片来源
        
        # 排版要求
        - 确保所有文字不互相遮挡、不超出框架
        - 遵循优秀PPT的基本排版原则
        - 文本大小合适，确保可读性
        - 合理利用空间，避免信息过于拥挤或稀疏
        
        # 输出格式
        请为每个分镜单独输出SVG代码，格式如下：
        
        ```svg
        <!-- 分镜 [序号]: [简短描述] -->
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080" width="1920" height="1080">
          <!-- 定义渐变、滤镜等 -->
          <defs>
            <!-- 定义样式和动画效果 -->
          </defs>
          
          <!-- 背景层 -->
          
          <!-- 内容层 -->
          
          <!-- 动画效果 -->
          
          <!-- 如有数据，标注数据来源 -->
          
          <!-- 如有图片，使用pic_path -->
          <image href="{pic_path}" x="[坐标]" y="[坐标]" width="[宽度]" height="[高度]">
            <title>图片来源：[来源]</title>
          </image>
        </svg>
        
        制作要点
        1.确保SVG代码完整且有效，可直接用于渲染
        2.所有分镜保持视觉风格一致性
        3.动画效果应简洁有效，突出重点内容
        4.代码应包含适当注释，便于后期编辑
        5.确保所有元素都有合适的id和class
"""