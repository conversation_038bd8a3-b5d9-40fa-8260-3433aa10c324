import json
import os
import time
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager, safe_parse_json
from utils.prompt_utils import PromptManager
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor

class NewsReportGenerator:
    """新闻报告分析节点，生成结构化的新闻报告JSON数据"""

    def __init__(self):
        """初始化新闻报告分析节点"""
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.data_dir = os.path.join('data')

    def extract_json_from_text(self, text: str) -> str:
        """
        从文本中提取JSON部分

        Args:
            text: 包含JSON的文本

        Returns:
            str: 提取的JSON字符串
        """
        import re

        if not text:
            return ""

        # 尝试查找```json和```之间的内容
        json_pattern = r'```json\s*([\s\S]*?)\s*```'
        matches = re.findall(json_pattern, text)

        if matches:
            # 返回第一个匹配的JSON内容
            json_content = matches[0].strip()
            # 修复可能的不完整JSON
            return self._fix_incomplete_json(json_content)

        # 如果没有找到```json标记，尝试查找{开头和}结尾的内容
        if text.strip().startswith('{') and text.strip().endswith('}'):
            return self._fix_incomplete_json(text.strip())

        # 尝试查找第一个{和最后一个}之间的内容
        if '{' in text and '}' in text:
            start = text.find('{')
            end = text.rfind('}') + 1
            return self._fix_incomplete_json(text[start:end])

        # 如果都没找到，返回原始文本
        return text

    def _fix_incomplete_json(self, json_str: str) -> str:
        """
        修复不完整的JSON字符串

        Args:
            json_str: 可能不完整的JSON字符串

        Returns:
            str: 修复后的JSON字符串
        """
        # 检查括号是否平衡
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')

        # 修复不平衡的括号
        fixed_str = json_str

        # 添加缺少的右大括号
        if open_braces > close_braces:
            fixed_str += '}' * (open_braces - close_braces)

        # 添加缺少的右方括号
        if open_brackets > close_brackets:
            fixed_str += ']' * (open_brackets - close_brackets)

        # 处理可能的截断JSON
        if fixed_str.rstrip().endswith(','):
            fixed_str = fixed_str.rstrip(',')

            # 如果最后一个逗号在数组中，添加结束括号
            if open_brackets > close_brackets:
                fixed_str += ']'

            # 如果最后一个逗号在对象中，添加结束大括号
            if open_braces > close_braces:
                fixed_str += '}'

        return fixed_str

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据，生成新闻报告JSON

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】新闻报告分析 (NewsReportGenerator)")
        print("="*50)

        # 准备查询数据
        trend_word, query, assets = self._prepare_query_data(state)
        
        # 生成新闻报告
        news_report, response = await self._generate_news_report(state, query)
        
        # 更新状态并保存结果
        self._update_and_save_state(state, news_report, assets, response)

        print("="*50)
        print("【完成执行】新闻报告分析 (NewsReportGenerator)")
        print("="*50 + "\n")

        return state
        
    def _prepare_query_data(self, state: Dict[str, Any]) -> Tuple[str, str, List[Dict]]:
        """
        准备查询数据
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[str, str, List[Dict]]: 热词、查询内容和资产列表
        """
        # 从 state 中获取热词列表
        hot_words = state["data"]["extracted_data"]["hot_word"]
        print(f"获取到热词数量: {len(hot_words)}")

        # 获取所有 5W1H 分析内容
        analyze_results = state["data"]["extracted_data"]["analyze_5w1h"]

        # 拼接所有分析内容
        combined_content = []
        for hot_word in hot_words:
            if hot_word in analyze_results:
                content = analyze_results[hot_word]
                combined_content.append(content)

        # 用 "---" 分隔所有内容
        _5w1hs = "\n---\n".join(combined_content)
        trend_word = state["data"]["trend_word"]
        
        # 从 assets.json 文件读取资产数据
        assets = self._load_assets()
        
        # 构建查询内容
        query = """热搜的核心主题：{trend_word}

        核心主题相关的 5W1Hs 分析：{_5w1hs}

        该热搜话题事件里面的主要图片资产素材：{assets}""".format(trend_word=trend_word, _5w1hs=_5w1hs, assets=assets)
        
        return trend_word, query, assets
        
    def _load_assets(self) -> List[Dict]:
        """
        加载资产数据
        
        Returns:
            List[Dict]: 资产列表
        """
        # 数据文件路径
        data_file_path = os.path.join(self.data_dir, 'assets.json')
        print(f"读取数据文件: {data_file_path}")

        # 读取 data.json 文件
        with open(data_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        assets = data.get('assets', [])
        print("已从 assets.json 读取资产数据:", assets)
        
        return assets
        
    async def _generate_news_report(self, state: Dict[str, Any], query: str) -> Tuple[Dict[str, Any], str]:
        """
        生成新闻报告
        
        Args:
            state: 当前状态
            query: 查询内容
            
        Returns:
            Tuple[Dict[str, Any], str]: 新闻报告和响应内容
        """
        # 获取提示词
        prompt = self.prompt_manager.get_prompt("1")
        print("已加载提示词模板")

        # 创建消息
        state["messages"] = []
        messages = self.llm.create_messages(
            system_prompt=prompt,
            history=state["messages"],
            query=query
        )

        # 生成报告，包含重试逻辑
        news_report, response = await self._generate_with_retry(messages)
        
        return news_report, response
        
    async def _generate_with_retry(self, messages: List[BaseMessage]) -> Tuple[Dict[str, Any], str]:
        """
        带重试逻辑的生成过程
        
        Args:
            messages: 消息列表
            
        Returns:
            Tuple[Dict[str, Any], str]: 新闻报告和响应内容
        """
        # 设置最大重试次数
        max_retries = 5
        retry_count = 0
        news_report = {}
        response = ""

        while retry_count < max_retries:
            try:
                # 生成响应
                response = await self.llm.generate_response(messages)
                print(f"分析结果，第{retry_count + 1}次尝试")

                # 检查响应是否为fail
                if response == "fail":
                    print(f"生成响应失败，第{retry_count + 1}次尝试")
                    retry_count += 1
                    continue

                # 尝试提取JSON部分
                json_content = self.extract_json_from_text(response)

                # 解析响应为JSON
                news_report = safe_parse_json(json_content)

                # 如果成功解析到非空的JSON，则跳出循环
                if news_report and len(news_report) > 0:
                    print(f"成功解析JSON，第{retry_count + 1}次尝试")
                    break

                print(f"解析结果为空，第{retry_count + 1}次尝试失败")
                retry_count += 1

            except Exception as e:
                print(f"生成或解析过程出错: {e}")
                retry_count += 1
                print(f"正在进行第{retry_count + 1}次尝试...")

            # 如果重试失败，等待一秒再试
            if retry_count < max_retries:
                time.sleep(1)

        # 如果所有重试都失败，使用默认空对象
        if retry_count >= max_retries and (not news_report or len(news_report) == 0):
            print(f"经过{max_retries}次尝试，仍然无法获取有效的JSON数据")
            news_report = self._create_default_news_report()
            
        return news_report, response
        
    def _create_default_news_report(self) -> Dict[str, Any]:
        """
        创建默认的新闻报告
        
        Returns:
            Dict[str, Any]: 默认的新闻报告
        """
        return {
            "title": "新闻报告生成失败",
            "summary": "无法从API获取有效的分析结果",
            "scenes": []
        }
        
    def _update_and_save_state(self, state: Dict[str, Any], news_report: Dict[str, Any], assets: List[Dict], response: str) -> None:
        """
        更新状态并保存结果
        
        Args:
            state: 当前状态
            news_report: 新闻报告
            assets: 资产列表
            response: 响应内容
        """
        # 更新状态
        state["data"]["news_report"] = news_report
        state["data"]["assets"] = assets
        state["current_step"] = "news_report_generator"

        # 保存分析结果
        self.file_manager.write_json("news_report.json", news_report)
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存新闻报告到: news_report.json")

