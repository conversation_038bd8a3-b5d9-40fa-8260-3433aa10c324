from typing import Dict, Any, List, Optional, Union, TypedDict

class Asset(TypedDict):
    """资产类型定义"""
    name: str
    description: str
    aspectRatio: str
    image_type: str
    path: str
    s3_url: Optional[str]

class Assets(TypedDict):
    """资产集合类型定义"""
    characterAssets: List[Asset]
    propAssets: List[Asset]

class XMLImage(TypedDict):
    """XML图片类型定义"""
    name: str
    type: str  # 'character' 或 'prop'
    description: str
    aspect_ratio: str
    image_type: str
    path: str
    s3_url: Optional[str]

class Subsection(TypedDict):
    """子章节类型定义"""
    title: str
    content: str
    key_points: List[str]
    assets: Assets
    section_script: Optional[str]
    conversation_log: Optional[List[Dict[str, str]]]
    svg_prompt: Optional[str]
    svg_code: Optional[str]
    svg_path: Optional[str]
    svg_code_review_conversation_log: Optional[List[Dict[str, str]]]
    xml_images: Optional[List[XMLImage]]

class Section(TypedDict):
    """章节类型定义"""
    title: str
    type: Optional[str]  # 'introduction', 'body', 'conclusion' 等
    content: Optional[str]
    subsections: Optional[List[Subsection]]
    assets: Optional[Assets]
    conversation_log: Optional[List[Dict[str, str]]]
    xml_images: Optional[List[XMLImage]]

class NewsReport(TypedDict):
    """新闻报告类型定义"""
    title: str
    topic_type_determined: str
    sections: List[Section] 