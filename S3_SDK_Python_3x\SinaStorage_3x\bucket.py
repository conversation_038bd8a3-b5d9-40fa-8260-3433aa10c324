import json
from urllib.parse import quote_plus
import hmac
import hashlib
import multiprocessing
import requests
import requests.adapters
from contextlib import closing
from base64 import b64encode
from multiprocessing import Pool
import os
import SinaStorage_3x
from SinaStorage_3x.filechunkio import FileChunkIO
from SinaStorage_3x.utils import rfc822_parsedate,rfc822_fmtdate,_amz_canonicalize,aws_urlquote,aws_md5, expire2Datetime, guess_mimetype, metadata_headers, getSize, make_preamb, _res_canonicalize


sinastorage_domain = "intra-d.sinastorage.com"


class ACL(object):
    ACL_GROUP_ANONYMOUSE = 'GRPS000000ANONYMOUSE'  # 匿名用户组
    ACL_GROUP_CANONICAL = 'GRPS0000000CANONICAL'  # 全部认证通过的用户
    #  write | write_acp | read | read_acp
    ACL_WRITE = 'write'
    ACL_WRITE_ACP = 'write_acp'
    ACL_READ = 'read'
    ACL_READ_ACP = 'read_acp'


class FAST_ACL:
    ACL_PRIVATE = "private"
    ACL_PUBLIC_READ = "public-read"
    ACL_PUBLIC_READ_WRITE = "public-read-write"
    ACL_AUTHED_READ = "authenticated-read"


def multi_callback(partId):
    print("part %s ，start runing！" % partId)


class SCSBucket(object):

    default_encoding = "utf-8"

    def __init__(self, bucket_name=None, base_url=None, timeout=60, secure=False, n_retries=3):
        if SinaStorage_3x.getDefaultAppInfo() is not None:
            self.access_key = SinaStorage_3x.getDefaultAppInfo().access_key
            self.secret_key = SinaStorage_3x.getDefaultAppInfo().secret_key
            self.secure = SinaStorage_3x.getDefaultAppInfo().secure
        else:
            if 'S3_ACCESS_KEY_ID' in os.environ and 'S3_SECRET_ACCESS_KEY' in os.environ:
                self.access_key = os.environ.get('S3_ACCESS_KEY_ID')
                self.secret_key = os.environ.get('S3_SECRET_ACCESS_KEY')
                self.secure = True
            else:
                raise ValueError("access_key and secret_key must not be None! \n\
                Please set sinastorage.setDefaultAppInfo('access_key', 'secret_key') \n\
                or set S3_ACCESS_KEY_ID and S3_SECRET_ACCESS_KEY in Environment first!")

        scheme = ("http", "https")[int(bool(self.secure))]

        if not base_url:
            base_url = "%s://%s/" % (scheme, sinastorage_domain)
            if bucket_name:
                base_url += "%s/" % aws_urlquote(bucket_name)

        elif secure is not None:
            if not base_url.startswith(scheme + "://"):
                raise ValueError("secure=%r, url must use %s"
                                 % (secure, scheme))
        self.bucket_name = bucket_name
        self.base_url = base_url
        self.timeout = timeout
        self.n_retries = n_retries
        self.temp_etag_list = [] # 分片上传用，因此，一个SCSBucket实例，只进行一个分片上传任务，否则会报错

    def __str__(self):
        return "<%s %s at %r>" % (self.__class__.__name__, self.bucket_name, self.base_url)

    def __repr__(self):
        return self.__class__.__name__ + "(%r, access_key=%r, base_url=%r)" % (
            self.bucket_name, self.access_key, self.base_url)

    def sign(self, need_sign):
        key = self.secret_key.encode("utf-8")
        hasher = hmac.new(key, need_sign.encode("utf-8"), hashlib.sha1)
        return (b64encode(hasher.digest())).decode("utf-8")[5:15]

    def get_need_sign(self, http_method, headers, object_name=None, sub_resource=None):
        preamb = make_preamb(http_method, headers)
        amz_header = _amz_canonicalize(headers)
        res = _res_canonicalize(self.bucket_name, object_name, sub_resource)
        need_sign = preamb + amz_header + res
        return need_sign

    def get_auth_header(self, need_sign):
        header_auth = "SINA %s:%s" % (self.access_key, self.sign(need_sign))
        return header_auth

    @staticmethod
    def init_header():
        headers = {"Date": rfc822_fmtdate(), "Host": sinastorage_domain}
        return headers

    @staticmethod
    def set_response_format(format_json=True):
        if not format_json:
            return None
        return {"formatter": "json"}

    def request_s3(self, http_method, request_url, headers, param=None, data=None, timeout=None, stream=False):
        req = getattr(requests, http_method)
        timeout = timeout if timeout else self.timeout
        try:
            response = req(request_url, headers=headers, params=param, data=data, timeout=timeout, stream=stream)
            return response
        except Exception as e:
            print(e)

    def make_request_url(self, object_name=None, sub_resource=None):
        request_url = self.base_url
        if object_name:
            request_url += aws_urlquote(object_name)
        if sub_resource:
            request_url += "?%s" % sub_resource
        return request_url

    @staticmethod
    def format_response(response):
        pass

    def delete_bucket(self):
        if not self.bucket_name:
            raise ValueError("delete bucket,name can't be None!!!")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="DELETE", headers=headers)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="delete",
                                   request_url=self.make_request_url(),
                                   headers=headers,
                                   param=self.set_response_format())
        print("delete bucket:%s" % self.bucket_name, response.status_code, response.headers)
        return response.status_code

    # defalut: private
    def put_bucket(self, acl=FAST_ACL.ACL_PRIVATE):
        if not self.bucket_name:
            raise ValueError("put bucket,name can't be None!!!")
        headers = self.init_header()
        headers.update({"x-amz-acl": acl})
        need_sign = self.get_need_sign(http_method="PUT", headers=headers)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(),
                                   headers=headers,
                                   param=self.set_response_format())
        print("put bucket:%s" % self.bucket_name, response.status_code, response.text)
        return response.status_code

    # params must be None
    def list_bucket(self):
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="GET", headers=headers)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(),
                                   headers=headers,
                                   param=self.set_response_format())
        print("List Bucket:", response.status_code, response.text)
        return response

    def get_bucket_meta(self):
        if not self.bucket_name:
            raise ValueError("get bucket meta, bucket name can't be None!!!")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="GET", headers=headers, sub_resource="meta")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(sub_resource="meta"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("get bucket:%s meta" % self.bucket_name, response.status_code, response.headers, response.text)
        return response.status_code

    def list_object(self, delimiter=None, marker=None, max_keys=None, prefix=None):
        if not self.bucket_name:
            raise ValueError("get bucket meta, bucket name can't be None!!!")
        headers = self.init_header()
        subresource = []
        if delimiter:
            subresource.append('delimiter=%s' % delimiter)
        if marker:
            subresource.append('marker=%s' % marker)
        if max_keys:
            subresource.append('max-keys=%s' % max_keys)
        if prefix:
            subresource.append('prefix=%s' % prefix)
        sub = None
        if len(subresource) > 0:
            sub = '&'.join(subresource)
        need_sign = self.get_need_sign(http_method="GET", headers=headers)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(sub_resource=sub),
                                   headers=headers,
                                   param=self.set_response_format())
        print("bucket:%s object lists:" % self.bucket_name, response.status_code, response.text)
        return response.text

    def get_bucket_acl(self):
        if not self.bucket_name:
            raise ValueError("get bucket acl,bucket name can't be None!!!")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="GET", headers=headers, sub_resource="acl")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(sub_resource="acl"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("get bucket acl:%s" % self.bucket_name, response.status_code, response.text)
        return response.status_code

    def set_bucket_acl(self, acl):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None!!!")
        headers = self.init_header()
        acl_json = json.dumps(acl)
        headers.update({"Content-Type": "application/json", "Content-Length": str(len(acl_json))})
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, sub_resource="acl")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(sub_resource="acl"),
                                   headers=headers,
                                   data=acl_json,
                                   param=self.set_response_format())
        print("set bucket:%s acl" % self.bucket_name, response.status_code, response.headers)
        return response.status_code


    # if lifecycle_dt['stop'] is 1: Stop this bucket's del process.
    # else: Restart this bucket's process.
    # About ten minutes delay.
    def set_bucket_lifecycle(self, lifecycle_dt):

        if not self.bucket_name:
            raise ValueError("bucket name can't be None!")

        headers = self.init_header()
        lifecycle_body = json.dumps(lifecycle_dt)
        headers.update({'Content-Type': 'application/json', 'Content-Length': str(len(lifecycle_body))})

        need_sign = self.get_need_sign(http_method='PUT', headers=headers, sub_resource='lifecycle')
        headers.update({'Authorization': self.get_auth_header(need_sign)})

        response = self.request_s3(http_method='put',
                                   request_url=self.make_request_url(sub_resource='lifecycle'),
                                   headers=headers,
                                   data=lifecycle_body,
                                   param=self.set_response_format())
        print('set bucket:%s lifecycle' % self.bucket_name, response.status_code, response.headers)
        return response.status_code


    def get_bucket_lifecycle(self):

        headers = self.init_header()
        need_sign = self.get_need_sign(http_method='GET', headers=headers, sub_resource='lifecycle')
        headers.update({'Authorization': self.get_auth_header(need_sign)})
        response = self.request_s3(http_method='get',
                                   request_url=self.make_request_url(sub_resource='lifecycle'),
                                   headers=headers,
                                   param=self.set_response_format())
        print('get bucket:%s lifecycle' % self.bucket_name, response.status_code, response.text)
        return response.status_code



    def delete_bucket_lifecycle(self):

        headers = self.init_header()
        need_sign = self.get_need_sign(http_method='DELETE', headers=headers, sub_resource='lifecycle')
        headers.update({'Authorization': self.get_auth_header(need_sign)})
        response = self.request_s3(http_method='delete',
                                   request_url=self.make_request_url(sub_resource='lifecycle'),
                                   headers=headers,
                                   param=self.set_response_format())
        print('delete bucket:%s lifecycle' % self.bucket_name, response.status_code, response.headers)
        return response.status_code

    '''
        获取一个已经存在文件的附加meta信息
        get_object_meta
    '''
    def get_object_meta(self, object_name):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="GET", headers=headers, object_name=object_name, sub_resource="meta")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource="meta"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("get object:%s meta info" % object_name, response.status_code, response.content)
        return response.status_code

    '''
        head 方式获取object meta
    '''
    def head_object_meta(self, object_name):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="HEAD", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="head",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format())
        print("head object:%s meta info" % object_name, response.status_code, response.headers)
        return response.status_code


    '''
        download file
        get_object 
        local_url:本地文件地址,当下载文件较大时设置stream: True
    '''

    def get_object(self, object_name, local_url, stream=False, range=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        if range:
            headers.update({"Range": range})
        need_sign = self.get_need_sign(http_method="GET", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format(),
                                   stream=stream)
        if response.status_code == 200:
            if stream:
                with closing(response):
                    chunk_size = 1024*1024   # 单次请求最大值
                    with open(local_url, "wb") as file:
                        for data in response.iter_content(chunk_size=chunk_size):
                            file.write(data)
            else:
                with open(local_url, "wb") as file:
                    file.write(response.content)
        return response.status_code

    def put_object_by_content(self, object_name, content, acl=FAST_ACL.ACL_PRIVATE, meta=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        if isinstance(content, str):
            content = content.encode("utf-8")
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": acl})
        if meta:
            headers.update(metadata_headers(meta))
        headers.update({"Content-Length": str(len(content))})
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format(),
                                   data=content)
        print("put object:%s by content:" % object_name, response.status_code, response.headers)
        return response.status_code

    def put_object(self, object_name, local_url, acl=FAST_ACL.ACL_PRIVATE, meta=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        with open(local_url, 'rb') as fp:
            headers["s-sina-sha1"] = aws_md5(fp)
            content = fp.read()
        headers.update({"Content-Length": str(len(content))})
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": FAST_ACL.ACL_PRIVATE})
        if meta:
            headers.update(metadata_headers(meta))
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format(),
                                   data=content)
        print("put object:%s" % object_name, response.status_code, response.headers, response.text)
        return response.status_code

    '''
        set_object_acl
        acl:json format
    '''
    def set_object_acl(self, object_name, acl):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        acl_json = json.dumps(acl)
        headers.update({"Content-Type": "application/json", "Content-Length": str(len(acl_json))})
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name, sub_resource="acl")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource="acl"),
                                   headers=headers,
                                   data=acl_json,
                                   param=self.set_response_format())
        print("set object:%s acl" % object_name, response.status_code, response.headers)
        return response.status_code

    def get_object_acl(self, object_name):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="GET", headers=headers, object_name=object_name, sub_resource="acl")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource="acl"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("get object:%s acl" % object_name, response.status_code, response.text)
        return response.status_code

    def delete_object(self, object_name):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        need_sign = self.get_need_sign(http_method="DELETE", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="delete",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format())
        print("delete object:%s ---" % object_name, response.status_code, response.headers)
        return response.status_code

    '''
        set_object_meta
        metaData:dict 
    '''
    def set_object_meta(self, object_name, meta_data):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        headers.update(metadata_headers(meta_data))
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name, sub_resource="meta")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource="meta"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("set object:%s meta---" % object_name, response.status_code, response.headers)
        return response.status_code

    '''
        copy_object
        sourthUrl:源文件地址，格式：/source-bucket/source-object，需要整体进行urlencode编码.
        directive:"COPY"使用源文件的meta而忽略本次上传的meta,"REPLACE"：使用本次上传的meta
    '''
    def copy_object(self, object_name, sourth_url, directive="COPY", acl=FAST_ACL.ACL_PRIVATE, meta=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": FAST_ACL.ACL_PRIVATE})
        if meta:
            headers.update(metadata_headers(meta))
        headers.update({"x-amz-copy-source": quote_plus("/"+sourth_url)})
        if directive:
            headers.update({"x-amz-metadata-directive": directive})
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format())
        print("copy object:from %s to %s" % (sourth_url, object_name), response.status_code, response.headers)
        return response.status_code

    '''
        multipart/uploads
    '''
    def init_multipart_upload(self, object_name, acl=FAST_ACL.ACL_PRIVATE, sub_resource="multipart", meta_data=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": FAST_ACL.ACL_PRIVATE})
        if meta_data:
            headers.update(metadata_headers(meta_data))
        need_sign = self.get_need_sign(http_method="POST", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        print("start init multi upload: %s-----" % object_name)
        response = self.request_s3(http_method="post",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource=sub_resource),
                                   headers=headers,
                                   param=self.set_response_format())
        print("init multi upload result:%s, %s, %s-----" % (object_name, response.status_code, response.text))
        if response.status_code == 200:
            return response.json()["UploadId"]
        return response.status_code

    '''
        multipart_upload
        sourcePath:local file URL 
        uploadId:init_multipart_upload return  uploadId
        coreNumbers:cores of CPU,default 4
        The same bucket cannot run two multipart_upload at the same time
    '''
    def multipart_upload(self, object_name, source_path, upload_id, core_numbers=multiprocessing.cpu_count()):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        source_size = getSize(source_path)
        parallel_processes = core_numbers  # 默认4个进程
        min_bytes_per_chunk = 5 * 1024 * 1024  # 分片最小文件大小
        bytes_per_chunk = max(int(math.sqrt(min_bytes_per_chunk) * math.sqrt(source_size)),
                              min_bytes_per_chunk)
        chunk_amount = int(math.ceil(source_size / float(bytes_per_chunk)))
        print("file_size---%s,divided into-%s-pieces，chunk_size---%s" % (source_size, chunk_amount, bytes_per_chunk))
        temp_list = []
        pool = Pool(processes=parallel_processes)  # 开启与核心数相同进程池
        i = 0
        while i < chunk_amount:
            offset = i * bytes_per_chunk
            remaining_bytes = source_size - offset
            chunk_bytes = min(bytes_per_chunk, remaining_bytes)
            temp = pool.apply_async(func=self.upload_part,
                                    args=(object_name, i+1, upload_id, chunk_bytes, offset, source_path,),
                                    callback=multi_callback(i+1))
            temp_list.append(temp)
            i += 1
        print("main run end!")
        pool.close()
        pool.join()
        for temp in temp_list:
            self.temp_etag_list.append(temp.get())
        print("etag_list", self.temp_etag_list)

    def upload_part(self, object_name, part_number, upload_id, chunk_size, offset, source_path):
        headers = self.init_header()
        headers.update({"Content-Length": str(chunk_size)})
        with FileChunkIO(source_path, 'rb', offset=offset, bytes=chunk_size) as fp:
            headers.update({"s-sina-sha1": aws_md5(fp)})
            content = fp.read()
        sub_resource = "partNumber=%s&uploadId=%s" % (part_number, upload_id)
        need_sign = self.get_need_sign(http_method="PUT", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"partNumber": part_number, "uploadId": upload_id}
        param.update(self.set_response_format())
        print("block---%s,offset---%s,chunk_size---%s,upload start-----" % (part_number, offset, chunk_size),
              datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param,
                                   data=content)
        print("%s upload chunk result:%s,%s" % (part_number, response.status_code, response.headers))
        if response.status_code == 200:
            return {"PartNumber": part_number, "ETag": response.headers["ETag"], "Filename": "{partnum}.{etag}".
                format(partnum=part_number, etag=response.headers["etag"])}
        else:
            return None

    def complete_multipart_upload(self, object_name, upload_id):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        pnum_tag_json = json.dumps(self.temp_etag_list)
        headers.update({"Content-Type": "application/json", "Content-Length": str(len(pnum_tag_json))})
        sub_resource = "uploadId=%s" % upload_id
        need_sign = self.get_need_sign(http_method="POST", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"uploadId": upload_id}
        param.update(self.set_response_format())
        response = self.request_s3(http_method="post",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param,
                                   data=pnum_tag_json)
        print("Complete Multipart Upload-----", response.status_code, response.headers, response.text)
        self.temp_etag_list = []

    def list_parts(self, object_name, upload_id):
        headers = self.init_header()
        sub_resource = "uploadId=%s" % upload_id
        need_sign = self.get_need_sign(http_method="GET", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"uploadId": upload_id}
        param.update(self.set_response_format())
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param)
        print("upload parts lists-----", response.headers, response.text)
        if response.status_code == 200:
            return response.text
        return response.status_code

    def put_object_relax(self, object_name, s_sina_sha1, s_sina_length):
        headers = self.init_header()
        headers["s-sina-sha1"] = s_sina_sha1
        headers["s-sina-length"] = str(s_sina_length)
        need_sign = self.get_need_sign(http_method="PUT", headers=headers,
                                       object_name=object_name, sub_resource="relax")
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name, sub_resource="relax"),
                                   headers=headers,
                                   param=self.set_response_format())
        print("put object relax:", response.status_code, response.headers, response.text)
        return response.status_code

    # PUT GET POST HEAD DELETE
    def generate_authed_url(self, http_method, expires=5, object_name="", content_type=""):
        """
        http:// <Your-Bucket-Name>.gslb.sinastorage.cn/?KID=sina,<access_key>&Expires=1398873316&ssig=<ssig>&formatter=json
        for put and get
        """
        expire = expire2Datetime(datetime.timedelta(minutes=expires))
        expire = time.mktime(expire.timetuple()[:9])
        expire = str(int(expire))
        res = "/%s/" % aws_urlquote(self.bucket_name)
        if object_name:
            res += aws_urlquote(object_name)
        string_to_sign = http_method + '\n\n' + content_type + '\n' + expire + '\n' + res
        ssig = self.sign(string_to_sign)
        authed_url = "%s%s?KID=sina,%s&ssig=%s" % (self.base_url, aws_urlquote(object_name), self.access_key, aws_urlquote(ssig))
        authed_url += "&Expires=%s&formatter=json" % expire
        return authed_url

    """The following method is used only for the sina mail"""

    def put_object_by_ssk_file(self, local_url, object_name=None, acl=None, meta=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        with open(local_url, 'rb') as fp:
            headers["s-sina-sha1"] = aws_md5(fp)
            content = fp.read()
        headers.update({"Content-Length": str(len(content))})
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": acl})
        if meta:
            headers.update(metadata_headers(meta))

        if object_name:
            object_name = "ssk/" + object_name + "/"
        else:
            object_name = "ssk/"
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format(),
                                   data=content)
        print("put object:%s" % object_name, response.status_code, response.headers, response.text)
        return response.headers["x-sina-serverside-key"].strip("ssk/").strip("%3D").strip("%3D")

    def put_object_by_ssk_data(self, data, object_name=None, acl=FAST_ACL.ACL_PRIVATE, meta=None, mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        if isinstance(data, str):
            data = data.encode("utf-8")
        headers.update({"Content-Length": str(len(data))})
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name)})
        if acl:
            headers.update({"X-AMZ-ACL": acl})
        if meta:
            headers.update(metadata_headers(meta))

        if object_name:
            object_name = "ssk/" + object_name + "/"
        else:
            object_name = "ssk/"
        need_sign = self.get_need_sign(http_method="PUT", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format(),
                                   data=data)
        print("put object:%s" % object_name, response.status_code, response.headers, response.text)
        return response.headers["x-sina-serverside-key"].strip("ssk/").strip("%3D").strip("%3D")

    """ type: Can be specified as any suffix name to download"""
    def get_object_by_ssk(self, ssk, type=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        object_name = "ssk/" + ssk
        if type:
            object_name += type
        need_sign = self.get_need_sign(http_method="GET", headers=headers, object_name=object_name)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=self.set_response_format()
                                   )
        print("get object:%s" % object_name, response.status_code, response.headers, response.text)
        return response

    '''
    Guarantee atomic multipart upload, for use server side key multipart upload 
    sub_resource: uploads
    '''

    def atomic_multipart_upload(self, source_path, object_name=None, acl=FAST_ACL.ACL_PRIVATE, sub_resource="uploads",
                                meta_data=None, mimetype=None):
        upload_id = self.init_multipart_upload_ssk(object_name, acl=acl, sub_resource=sub_resource,
                                                   meta_data=meta_data, mimetype=mimetype)
        print("upload_id:", upload_id)
        if isinstance(upload_id, int):
            raise ValueError("status_code is not 200, get bad upload_id, init_multipart_upload failed!")
        chunk_amount = self.multipart_upload_ssk(object_name, source_path, upload_id)
        if len(self.temp_etag_list) != chunk_amount:
            self.temp_etag_list = []
            raise ValueError("count of local multi parts is different with upload! multi upload failed")
        list_parts_result = self.list_parts(object_name, upload_id)
        print(list_parts_result)
        resp = self.complete_multipart_upload_ssk(object_name, upload_id)
        if resp.status_code != 200:
            raise ValueError("merge files failed, http code is ", resp.status_code)

    def init_multipart_upload_ssk(self, object_name, acl=FAST_ACL.ACL_PRIVATE, sub_resource="uploads", meta_data=None,
                                  mimetype=None):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        headers.update({"Content-Type": mimetype if mimetype else guess_mimetype(object_name),
                        "x-sina-additional-indexed-key": object_name})
        object_name = 'ssk/' + object_name + '/'
        if acl:
            headers.update({"X-AMZ-ACL": acl})
        if meta_data:
            headers.update(metadata_headers(meta_data))
        need_sign = self.get_need_sign(http_method="POST", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        print("start init multi upload: %s-----" % object_name)
        print('headers:', headers)
        response = self.request_s3(http_method="post",
                                   request_url=self.make_request_url(object_name=object_name,
                                                                     sub_resource=sub_resource),
                                   headers=headers,
                                   param=self.set_response_format())
        print("init multi upload result:%s, %s,%s, %s-----" % (
        object_name, response.status_code, response.headers, response.text))
        if response.status_code == 200:
            return response.json()["UploadId"]
        return response.status_code

    def multipart_upload_ssk(self, object_name, source_path, upload_id, core_numbers=multiprocessing.cpu_count()):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        source_size = getSize(source_path)
        parallel_processes = core_numbers  # 默认4个进程
        min_bytes_per_chunk = 5 * 1024 * 1024  # 分片最小文件大小
        bytes_per_chunk = max(int(math.sqrt(min_bytes_per_chunk) * math.sqrt(source_size)),
                              min_bytes_per_chunk)
        chunk_amount = int(math.ceil(source_size / float(bytes_per_chunk)))
        print("file_size---%s,divided into-%s-pieces，chunk_size---%s" % (source_size, chunk_amount, bytes_per_chunk))
        temp_list = []
        pool = Pool(processes=parallel_processes)  # 开启与核心数相同进程池
        i = 0
        while i < chunk_amount:
            offset = i * bytes_per_chunk
            remaining_bytes = source_size - offset
            chunk_bytes = min(bytes_per_chunk, remaining_bytes)
            temp = pool.apply_async(func=self.upload_part_ssk,
                                    args=(object_name, i + 1, upload_id, chunk_bytes, offset, source_path,),
                                    callback=multi_callback(i + 1))
            temp_list.append(temp)
            i += 1
        print("main run end!")
        pool.close()
        pool.join()
        for temp in temp_list:
            if temp is not None:
                self.temp_etag_list.append(temp.get())
        print("etag_list", self.temp_etag_list)
        return chunk_amount

    def upload_part_ssk(self, object_name, part_number, upload_id, chunk_size, offset, source_path):
        headers = self.init_header()
        headers.update({"Content-Length": str(chunk_size)})
        object_name = 'ssk/' + object_name + '/'
        with FileChunkIO(source_path, 'rb', offset=offset, bytes=chunk_size) as fp:
            str_sha1 = aws_md5(fp)
            headers.update({"s-sina-sha1": str_sha1})
            content = fp.read()
        sub_resource = "partNumber=%s&uploadId=%s" % (part_number, upload_id)
        need_sign = self.get_need_sign(http_method="PUT", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"partNumber": part_number, "uploadId": upload_id}
        param.update(self.set_response_format())
        print("block---%s,offset---%s,chunk_size---%s,upload start-----" % (part_number, offset, chunk_size),
              datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        response = self.request_s3(http_method="put",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param,
                                   data=content)
        print("%s upload chunk result:%s,%s" % (part_number, response.status_code, response.headers))
        if response.status_code == 200:
            return {"PartNumber": part_number, "ETag": response.headers["ETag"], "Filename": "{partnum}.{etag}".
                format(partnum=part_number, etag=response.headers["etag"])}
        return None

    def complete_multipart_upload_ssk(self, object_name, upload_id):
        if not self.bucket_name:
            raise ValueError("bucket name can't be None")
        headers = self.init_header()
        pnum_tag_json = json.dumps(self.temp_etag_list)
        headers.update({"Content-Type": "text/json",
                        "Content-Length": str(len(pnum_tag_json)),
                        })
        object_name = 'ssk/' + object_name + '/'
        sub_resource = "uploadId=%s" % upload_id
        need_sign = self.get_need_sign(http_method="POST", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"uploadId": upload_id}
        param.update(self.set_response_format())
        response = self.request_s3(http_method="post",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param,
                                   data=pnum_tag_json)
        print("Complete Multipart Upload-----", response.status_code, response.headers, response.text)
        self.temp_etag_list = []
        return response

    def list_parts_ssk(self, object_name, upload_id):
        headers = self.init_header()
        sub_resource = "uploadId=%s" % upload_id
        need_sign = self.get_need_sign(http_method="GET", headers=headers,
                                       object_name=object_name, sub_resource=sub_resource)
        headers.update({"Authorization": self.get_auth_header(need_sign)})
        param = {"uploadId": upload_id}
        param.update(self.set_response_format())
        response = self.request_s3(http_method="get",
                                   request_url=self.make_request_url(object_name=object_name),
                                   headers=headers,
                                   param=param)
        print("upload parts lists-----", response.headers, response.text)
        if response.status_code == 200:
            return response.text
        return response.status_code
